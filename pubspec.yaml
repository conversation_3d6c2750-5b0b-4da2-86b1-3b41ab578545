name: avema_v2
description: "Fleet Management System for Avema - Version 2"

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 2.2.20+22202

environment:
  sdk: '>=3.3.4 <4.0.0'
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6

  # VIDEO
  video_player: ^2.9.5
  better_player_plus:
    git:
      url: https://github.com/huynvavema/better_player_plus.git
      ref: v1.0.7
  shimmer: ^3.0.0
  image_picker: ^1.1.2

  # Networking
  dio: 5.4.3+1
  http: 1.2.2
  jwt_decoder: 2.0.1
  elastic_client: 0.3.15

  # MAP
  flutter_map: 6.0.1
  flutter_map_marker_cluster: 1.3.6
  flutter_map_marker_popup: 6.1.2
  google_maps_flutter: 2.5.3
  google_maps_cluster_manager_2: 3.2.0
  latlong2: 0.9.1
  flutter_google_street_view:
    git:
      url: https://github.com/zyzdev/flutter_street_view.git
      path: flutter_google_street_view

  widget_to_marker: 1.0.5
  location: 5.0.0

  # SHARE
  share_plus: 10.0.2

  # DOWNLOAD
  image_gallery_saver:
    git:
      url: 'https://github.com/danilz-276/image_gallery_saver.git'

  # PERMISSION
  permission_handler: 11.3.1
  app_tracking_transparency: 2.0.6

  # INTERNET
  internet_connection_checker: 1.0.0+1
  connectivity_plus: 6.0.4

  # State Management
  flutter_bloc: 8.1.5
  equatable: 2.0.5
  go_router: 14.2.0
  rxdart: 0.28.0
  
  # Device and Package Info
  device_info_plus: 10.1.2
  package_info_plus: 8.0.2
  uuid: 4.4.0

  # Dependency Injection
  get_it: 7.7.0
  injectable: 2.4.2

  # UI
  auto_size_text: 3.0.0
  sliding_up_panel: 2.0.0+1
  flutter_svg: 2.0.10+1 
  image: 4.2.0
  flutter_datetime_picker_plus: 2.2.0
  flutter_cupertino_datetime_picker: 3.0.0

  sticky_headers: 0.3.0+2
  draggable_widget: ^2.0.0
  another_flushbar: 1.12.30 
  fluttertoast: 8.2.8
  flutter_local_notifications: 17.2.3
  overlay_support: 2.1.0
  flutter_animated_icons: ^1.0.1
  flutter_html: 3.0.0-beta.2
  shimmer_animation: ^2.1.0+1

  # ANIMATION
  # rive: 0.13.9 
  custom_refresh_indicator: 4.0.1

  # TIME
  flutter_timezone: 2.0.1
  timeago: 3.7.0 

  # Modal
  dartz: 0.10.1

  # FIREBASE
  firebase_core: 3.5.0
  firebase_analytics: 11.3.2
  firebase_crashlytics: 4.1.2
  firebase_messaging: 15.1.2
  firebase_messaging_web: 3.9.1

  web: 1.0.0

  # Local Storage
  shared_preferences: ^2.2.3

  # Serialization and Retrofit
  retrofit: 4.1.0
  intl: 0.19.0
  url_launcher: 6.2.6
  freezed_annotation: ^2.4.4
  geolocator: ^12.0.0

  # Multi Languages
  easy_localization: ^3.0.7
  path_provider: 2.1.3

  # Other
  wakelock_plus: 1.2.8
  lottie: ^1.4.3
  graphic: ^2.6.0
  data_table_2: ^2.5.16
  cached_network_image: ^3.4.0

dependency_overrides:
  firebase_core_platform_interface: 5.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

  # GEN
  freezed: 2.5.2
  json_serializable: 6.8.0
  build_runner: 2.4.10
  retrofit_generator: 8.1.0
  injectable_generator: 2.6.1
  # GEN Image
  flutter_gen_runner: 5.5.0+1



  



# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

# To add assets to your application, add an assets section, like this:
  assets:
    - lib/assets/
    - lib/assets/vehicle/
    - lib/assets/vehicle/png_1x/
    - lib/assets/translations/en-US.json
    - lib/assets/translations/vi-VN.json
    - lib/assets/translations/zh-TW.json
    # - lib/assets/1.5x/
    # - lib/assets/2.0x/
    # - lib/assets/3.0x/
    # - lib/assets/4.0x/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
  # flutter_gen:
  #   output: lib/assets_manager/gen/
  #   line_length: 80 # Độ dài tối đa của các dòng mã được tạo (tùy chọn)
