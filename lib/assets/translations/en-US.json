{"m2_monitor_screen": "Monitor", "m2_deselect_all": "Deselect all", "m2_select_all": "Select all", "m2_select_vehicle": "Select vehicle", "m2_view_all": "View all", "m2_history_waypoint": "History waypoint", "m2_reports": "Reports", "m2_settings": "Settings", "m2_notification": "Notification", "m2_about_us": "About us", "m2_management": "Management", "m2_please_select_vehicle": "Please select vehicle", "m2_please_select_vehicle_and_time": "Please select vehicle and time", "m2_select_date_and_time": "Select date and time", "m2_note": "Note", "m2_only_a_time_period_within_7_days_can_be_selected": "Only a time period within 7 days can be selected", "m2_plate": "Plate", "m2_group": "Group", "m2_from": "From", "m2_to": "To", "m2_quick_select": "Quick select", "m2_day": "day", "m2_hour": "Hour", "m2_minute": "Minute", "m2_second": "Second", "m2_today": "Today", "m2_yesterday": "Yesterday", "m2_select": "Select", "m2_overview": "Overview", "m2_route": "Route", "m2_stop": "Stop", "m2_idle": "Idle", "m2_duration": "Duration", "m2_distance": "Distance", "m2_maximum_speed": "Maximum speed", "m2_number_of_times_overspeeding": "Number of times overspeeding", "m2_number_of_stops_idle": "Number of stops idle", "m2_map_settings": "Map settings", "m2_map_type": "Map type", "m2_default": "<PERSON><PERSON><PERSON>", "m2_satellite": "Satellite", "m2_traffic": "Traffic", "m2_map_details": "Map details", "m2_stop_point": "Stop point", "m2_focus_to_vehicle": "Focus to vehicle", "m2_type_of_vehicle": "Type of vehicle", "m2_speed_​​according_to_route": "Speed according to route", "m2_timer": "Timer", "m2_view_vehicle_information": "View vehicle information", "m2_total_mileage": "Total mileage", "m2_overspeed_warning": "Overspeed warning", "m2_view_station": "View station", "m2_view_region": "View region", "m2_light": "Light", "m2_dark": "Dark", "m2_search_vehicle": "Search vehicle", "m2_lost_gps": "Lost gps", "m2_running": "Running", "m2_long_idle": "Long idle", "m2_overspeed": "Overspeed", "m2_acc_off": "Acc off", "m2_acc_on": "Acc on", "m2_address": "Address", "m2_location": "Location", "m2_daily_information": "Daily information", "m2_trip": "Trip", "m2_sensors": "Sensors", "m2_idle_time": "Idle time", "m2_number_of_idle": "Number of idle", "m2_stop_time": "Stop time", "m2_number_of_stop": "Number of stop", "m2_running_time": "Running time", "m2_turn_on_time": "Turn on time", "m2_max_speed": "Max speed", "m2_number_of_close_door": "Number of close door", "m2_number_of_open_door": "Number of open door", "m2_number_of_over_speed": "Number of over speed", "m2_total": "Total", "m2_start_time": "Start time", "m2_end_time": "End time", "m2_phone": "Phone", "m2_website": "Website", "m2_fax": "Fax", "m2_official_account": "Official account", "m2_update": "Update", "m2_driver": "Driver", "m2_window_livestream": "Window Livestream", "m2_livestream": "Livestream", "m2_playback": "Playback", "m2_theme": "Theme", "m2_no_history_route": "No history route", "m2_an_error_occurred_please_try_again": "An error occurred, please try again!", "m2_empty_list": "Empty list", "m2_time": "time", "m2_no_idle_stop_report": "No idle stop report", "m2_no_group": "No group", "m2_dont_have_permission": "You do not have access to this resource. Please check your permissions and try again!", "m2_account_not_been_verified": "Account has not been verified. Please log in and try again.", "m2_server_error": "Server error", "m2_locate": "Locate", "m2_session_expired": "Login session has expired, Please log in again!", "m2_language": "Language", "m2_full_name": "Full name", "m2_license_no": "License no", "m2_expired_date": "Expired date", "m2_account": "Account", "m2_password": "Password", "m2_login": "<PERSON><PERSON>", "m2_remember": "Remember", "m2_enter_account": "Enter account", "m2_enter_password": "Enter password", "m2_wrong_account_or_password": "Wrong account or password", "m2_unauthorized": "Unauthorized", "m2_unauthenticated": "Unauthenticated", "m2_choose_window_live": "<PERSON>ose Window Live", "m2_chose_channel": "Chose channel", "m2_the_maximum_number_of_live_streams_is_12": "The maximum number of live streams is 12", "m2_please_select_live_channel": "Please select live channel", "m2_street_view": "Street view", "m2_timeline": "Timeline", "m2_no_timeline": "No timeline!", "m2_user_information": "User information", "m2_email": "Email", "m2_logout": "Logout", "m2_do_you_want_to_logout": "Do you want to logout", "m2_cancel": "Cancel", "m2_change_password": "Change password", "m2_enter_old_password": "Enter old password", "m2_old_password": "Old password", "m2_enter_new_password": "Enter new password", "m2_new_password": "New password", "m2_confirm_new_password": "Confirm new password", "m2_email_cannot_be_empty": "Email cannot be empty", "m2_email_wrong_format": "Email wrong format", "m2_phone_cannot_be_empty": "Phone cannot be empty", "m2_phone_wrong_format": "Phone wrong format", "m2_phone_must_be_greater_than_6_digits": "Phone must be greater than 6 digits", "m2_phone_must_be_less_than_11_digits": "Phone must be less than 11 digits", "m2_update_information_success": "Update information success", "m2_old_password_cannot_be_empty": "Old password cannot be empty", "m2_wrong_old_password": "Wrong old password", "m2_new_password_cannot_be_empty": "New password cannot be empty", "m2_new_password_must_be_greater_than_6_digits": "New password must be greater than 6 digits", "m2_new_password_and_confirm_password_incorrect": "New password and confirm password incorrect", "m2_update_password_success": "Update password success!", "m2_change_information": "Change information", "m2_enter_email": "Enter email", "m2_enter_phone_number": "Enter phone number", "m2_enter": "Enter", "m2_hello": "Hello", "m2_change_theme": "Change theme", "m2_change_primary_color": "Change primary color", "m2_color": "Color", "m2_primary_color": "Primary color", "m2_adjust_height": "Adjust height", "m2_low_height": "Low", "m2_normal_height": "Normal", "m2_high_height": "High", "m2_point": "Point", "m2_orange": "Orange", "m2_pink": "Pink", "m2_blue": "Blue", "m2_green": "Green", "m2_cyan": "<PERSON><PERSON>", "m2_playback_query": "Playback query", "m2_device": "<PERSON><PERSON>", "m2_cloud": "Cloud", "m2_load": "Load", "m2_monday": "Monday", "m2_tuesday": "Tuesday", "m2_wednesday": "Wednesday", "m2_thursday": "Thursday", "m2_friday": "Friday", "m2_saturday": "Saturday", "m2_sunday": "Sunday", "m2_monday_short": "MON", "m2_tuesday_short": "TUE", "m2_wednesday_short": "WED", "m2_thursday_short": "THU", "m2_friday_short": "FRI", "m2_saturday_short": "SAT", "m2_sunday_short": "SUN", "m2_playback_can_view_15_days": "Playback can only be viewed within the last 15 days", "m2_please_select_vehicle_for_playback": "Please select a vehicle to view playback", "m2_please_choose_channel": "Please choose channel", "m2_please_try_again": "Please try again", "m2_please_choose_timeline": "Please choose timeline for playback", "m2_user": "User", "m2_oke": "<PERSON>e", "m2_please_select_other_time": "Please select other time", "m2_need_upgrade_version": "New version is available, please upgrade in App Store or Google Play", "m2_please_grant_permission": "Please grant permission to use this function!", "m2_guide_to_set_permission": "Go to SETTINGS of PHONE to set permission! Settings -> 噗噗嚨 -> Location -> Always", "m2_please_grant_notification_permission": "Please grant notification permission to use notification function!", "m2_vehicle_lost_gps": "We temp can't get gps location of vehicle", "m2_currently_vehicle_does_not_exist": "Currently vehicle does not exist", "m2_choose_date": "Choose date", "m2_all": "All", "m2_take_picture_success": "Take picture success!", "m2_take_picture_failure": "Take picture fail!", "m2_image": "Image", "m2_download_image_success": "Download image successfully!", "m2_download_image_fail": "Download image fail!", "m2_no_internet": "No Internet!", "m2_registerWithToken": "Register with token", "m2_pending": "Pending", "m2_no_list_video_data": "No list video data", "m2_share_locator": "Share locator", "m2_choose_time": "Choose time", "m2_last_modified": "Last modified", "m2_user_modified": "User modified", "m2_receive_share_locator": "Receive share locator by scan QR Code", "m2_scan_qr_code": "Scan QR code", "m2_or": "Or", "m2_enter_qr_code": "Enter QR code", "m2_confirm": "Confirm", "m2_guide_to_set_camera_permission": "Go to SETTINGS of PHONE to set permission! Settings -> 噗噗嚨 -> Camera -> On", "m2_please_grant_camera_permission": "Please grant camera permission for using camera", "m2_receive_share_successfully": "Receive share locator successfully!", "m2_receive_share_fail": "Fail to receive share locator!", "m2_preview_image": "Preview image", "m2_edit": "Edit", "m2_delete": "Delete", "m2_confirm_this_record": "Do you want to delete this record?", "m2_update_success": "Update success!", "m2_add_new_locator": "Add new locator", "m2_name": "Name", "m2_vehicle": "Vehicle", "m2_geofence": "Geofence", "m2_station": "Station", "m2_vehicle_status": "Vehicle status", "m2_option": "Option", "m2_duration_cannot_empty": "Duration cannot empty", "m2_name_cannot_empty": "Name cannot empty", "m2_next": "Next", "m2_share": "Share", "m2_status": "Status", "m2_condition_choose_locator_duration_time": "Must less than 367 days", "m2_duration_value_default": "10", "m2_duration_time_more_than_0": "Duration time must be greater than 0", "m2_create_new_success": "Create new success", "m2_create": "Create", "m2_expired": "Expired", "m2_active": "Active", "m2_detail": "Detail", "m2_delete_success": "Delete success!", "m2_notification_setting": "Notification Setting", "m2_fuel_pump": "Fuel Pump", "m2_turn_on_fuel_pump": "Turn on fuel pump", "m2_turn_off_fuel_pump": "Turn off fuel pump", "m2_sensor.output1.cmd.on": "", "m2_sensor.output1.cmd.off": "", "m2_on": "On", "m2_off": "Off", "m2_voltage": "Voltage", "m2_mileage": "<PERSON><PERSON>", "m2_management_vehicle": "Management Vehicle", "m2_device_type": "Device Type", "m2_vehicle_type": "Vehicle Type", "m2_unknown": "Unknown", "m2_product_year": "Product Year", "m2_fuel_per_100_km": "Fuel per 100 Km", "m2_fuel_idle_hour": "Fuel Idle Hour", "m2_fuel_ignition_hour": "Fuel Ignition Hour", "m2_start": "Start", "m2_end": "End", "m2_unnamed_road": "Unnamed Road", "m2_do_you_want_change_device_setting": "Bạn có muốn thay đổi cài đặt không?", "m2_all_vehicle": "All Vehicle", "m2_bus_setting": "Bus Setting", "m2_sent_to_goverment": "Sent to goverment", "m2_sent_to_driver": "Sent to driver", "m2_long_term_care_setting": "Long term care setting", "m2_special_car_go_setting": "Special car go setting", "m2_update_driver": "Update Driver", "m2_add_driver": "Add Driver", "m2_city": "City", "m2_driver_no": "Driver No", "m2_rfid": "RFID", "m2_register_place": "Register Place", "m2_register_date": "Register Date", "m2_is_activated": "Is Activated", "m2_search": "Search", "m2_search_driver": "Search by driver, phone, rfid, license no, ...", "m2_content_delete_driver": "Do you want to delete this record?", "m2_driver_information": "Driver Information", "m2_license_information": "License Information", "m2_value_is_required": "Value is required", "m2_no_data": "No data", "m2_date": "Date", "m2_tacho_graph_report": "Tacho Graph Report", "m2_show_full": "Show full", "m2_show_less": "Show less", "m2_rfid_report": "RFID Report", "m2_relutation": "Relutation", "m2_tradition": "Tradition", "m2_legend_for_speed": "Speed (Km/h)", "m2_legend_for_mileage": "Mileage (Km)", "m2_section_mileage": "Section mileage", "m2_invalid_coordinates": "Invalid coordinates", "m2_unexpected_error_in_panorama_change": "Unexpected error in panorama change"}