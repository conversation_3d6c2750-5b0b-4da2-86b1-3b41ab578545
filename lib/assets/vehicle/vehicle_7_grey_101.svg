<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_42_140776)">
<path d="M21.5 37C21.5 36.1716 22.1716 35.5 23 35.5H31.5C32.3284 35.5 33 36.1716 33 37V45C33 48.4478 34.3696 51.7544 36.8076 54.1924C39.2456 56.6304 42.5522 58 46 58C49.4478 58 52.7544 56.6304 55.1924 54.1924C57.6304 51.7544 59 48.4478 59 45V37C59 36.1716 59.6716 35.5 60.5 35.5H69C69.8284 35.5 70.5 36.1716 70.5 37V45C70.5 58.5314 59.5314 69.5 46 69.5C32.4686 69.5 21.5 58.5314 21.5 45L21.5 37Z" fill="#BDBEC0" stroke="white"/>
<rect x="21.5" y="23" width="11.5" height="9" rx="1.5" fill="#909295"/>
<rect x="21.5" y="23" width="11.5" height="9" rx="1.5" fill="white" fill-opacity="0.2"/>
<rect x="21.5" y="23" width="11.5" height="9" rx="1.5" stroke="white"/>
<rect x="59" y="23" width="11.5" height="9" rx="1.5" fill="#909295"/>
<rect x="59" y="23" width="11.5" height="9" rx="1.5" fill="white" fill-opacity="0.2"/>
<rect x="59" y="23" width="11.5" height="9" rx="1.5" stroke="white"/>
<g clip-path="url(#clip0_42_140776)">
<g filter="url(#filter1_ddd_42_140776)">
<g filter="url(#filter2_i_42_140776)">
<rect x="35.3333" y="61.3333" width="21.3333" height="21.3333" rx="10.6667" fill="#FF1010"/>
</g>
<rect x="35.8333" y="61.8333" width="20.3333" height="20.3333" rx="10.1667" stroke="#741646"/>
<g filter="url(#filter3_i_42_140776)">
<rect x="35.8333" y="61.8333" width="20.3333" height="20.3333" rx="10.1667" stroke="white"/>
</g>
<g filter="url(#filter4_f_42_140776)">
<ellipse cx="46" cy="66" rx="4" ry="2" fill="white"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_42_140776" x="-4" y="-2" width="100" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.211765 0 0 0 0 0.258824 0 0 0 0 0.345098 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_42_140776"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_42_140776" result="shape"/>
</filter>
<filter id="filter1_ddd_42_140776" x="34.3333" y="61.3333" width="24.3333" height="24.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_42_140776"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_42_140776" result="effect2_dropShadow_42_140776"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_42_140776" result="effect3_dropShadow_42_140776"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_42_140776" result="shape"/>
</filter>
<filter id="filter2_i_42_140776" x="35.3333" y="58.3333" width="21.3333" height="24.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_42_140776"/>
</filter>
<filter id="filter3_i_42_140776" x="35.3333" y="58.3333" width="21.3333" height="24.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_42_140776"/>
</filter>
<filter id="filter4_f_42_140776" x="38.3" y="60.3" width="15.4" height="11.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.85" result="effect1_foregroundBlur_42_140776"/>
</filter>
<clipPath id="clip0_42_140776">
<rect width="24" height="24" fill="white" transform="translate(34 60)"/>
</clipPath>
</defs>
</svg>
