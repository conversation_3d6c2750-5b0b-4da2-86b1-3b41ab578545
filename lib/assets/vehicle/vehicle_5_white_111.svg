<svg width="92" height="99" viewBox="0 0 92 99" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_38_130361)" filter="url(#filter0_d_38_130361)">
<path d="M59.3209 22.8436V5.82696C59.3209 5.07233 58.7094 4.46087 57.9552 4.46087H53.385H38.7357H34.166C33.4114 4.46087 32.7999 5.07233 32.7999 5.82696V22.8436H59.3209Z" fill="#F3F3F4"/>
<path d="M59.3209 22.8436V5.82696C59.3209 5.07233 58.7094 4.46087 57.9552 4.46087H53.385H38.7357H34.166C33.4114 4.46087 32.7999 5.07233 32.7999 5.82696V22.8436H59.3209Z" fill="black" fill-opacity="0.2"/>
<path d="M33.8195 92.5239H58.8789C59.9997 92.5239 60.9082 91.6154 60.9082 90.4951V24.596C60.9082 23.4752 59.9997 22.5667 58.8789 22.5667H33.8195C32.6992 22.5667 31.7906 23.4752 31.7906 24.596V90.4951C31.7906 91.6154 32.6992 92.5239 33.8195 92.5239Z" fill="#F3F3F4"/>
<path d="M33.8195 92.5239H58.8789C59.9997 92.5239 60.9082 91.6154 60.9082 90.4951V24.596C60.9082 23.4752 59.9997 22.5667 58.8789 22.5667H33.8195C32.6992 22.5667 31.7906 23.4752 31.7906 24.596V90.4951C31.7906 91.6154 32.6992 92.5239 33.8195 92.5239Z" fill="black" fill-opacity="0.2"/>
<path d="M36.3924 89.6927H56.3059C57.1965 89.6927 57.9186 88.8213 57.9186 87.746V24.5139C57.9186 23.4386 57.1965 22.5667 56.3059 22.5667H36.3924C35.5022 22.5667 34.7802 23.4386 34.7802 24.5139V87.746C34.7802 88.8213 35.5022 89.6927 36.3924 89.6927Z" fill="#F3F3F4"/>
<path d="M27.6396 21.7157C27.4073 21.7157 27.2195 21.5279 27.2195 21.296V19.9741C27.2195 19.3586 27.7208 18.8573 28.3358 18.8573H32.7998C33.0322 18.8573 33.22 19.0451 33.22 19.2774C33.22 19.5093 33.0322 19.6971 32.7998 19.6971H28.3358C28.1837 19.6971 28.0593 19.8215 28.0593 19.9741V21.296C28.0593 21.5279 27.8711 21.7157 27.6396 21.7157Z" fill="#CBD6DE"/>
<path d="M29.2886 21.2961H26.019C25.4695 21.2961 25.0239 21.7417 25.0239 22.2911H30.2841C30.2841 21.7417 29.8385 21.2961 29.2886 21.2961Z" fill="#CBD6DE"/>
<path d="M37.7898 87.6456C37.5597 87.6456 37.3733 87.4592 37.3733 87.2295V25.4595C37.3733 25.2293 37.5597 25.0434 37.7898 25.0434C38.0195 25.0434 38.206 25.2293 38.206 25.4595V87.2295C38.206 87.4592 38.0195 87.6456 37.7898 87.6456Z" fill="#C2C2C3"/>
<path d="M43.3036 87.6456C43.0735 87.6456 42.8871 87.4592 42.8871 87.2295V25.4595C42.8871 25.2293 43.0735 25.0434 43.3036 25.0434C43.5338 25.0434 43.7202 25.2293 43.7202 25.4595V87.2295C43.7202 87.4592 43.5338 87.6456 43.3036 87.6456Z" fill="#C2C2C3"/>
<path d="M46.0604 87.8552C45.8302 87.8552 45.6438 87.6692 45.6438 87.4391V25.4594C45.6438 25.2293 45.8302 25.0433 46.0604 25.0433C46.29 25.0433 46.4765 25.2293 46.4765 25.4594V87.4391C46.4765 87.6692 46.29 87.8552 46.0604 87.8552Z" fill="#C2C2C3"/>
<path d="M40.5465 87.8552C40.3168 87.8552 40.1304 87.6692 40.1304 87.4391V25.4594C40.1304 25.2293 40.3168 25.0433 40.5465 25.0433C40.7766 25.0433 40.963 25.2293 40.963 25.4594V87.4391C40.963 87.6692 40.7766 87.8552 40.5465 87.8552Z" fill="#C2C2C3"/>
<path d="M37.0356 5.32483H35.6405C35.1638 5.32483 34.7771 4.9377 34.7771 4.46092H37.8995C37.8995 4.9377 37.5129 5.32483 37.0356 5.32483Z" fill="white"/>
<path d="M36.0303 91.8937H34.2258C34.0019 91.8937 33.8195 92.0757 33.8195 92.3V92.5239H36.4366V92.3C36.4366 92.0757 36.2546 91.8937 36.0303 91.8937Z" fill="#CF0000"/>
<path d="M64.481 21.7157C64.2495 21.7157 64.0613 21.5279 64.0613 21.296V19.9741C64.0613 19.8215 63.9373 19.6971 63.7848 19.6971H59.3208C59.0889 19.6971 58.9011 19.5093 58.9011 19.2774C58.9011 19.0451 59.0889 18.8573 59.3208 18.8573H63.7848C64.4003 18.8573 64.9011 19.3586 64.9011 19.9741V21.296C64.9011 21.5279 64.7134 21.7157 64.481 21.7157Z" fill="#CBD6DE"/>
<path d="M62.8322 21.2961H66.1018C66.6517 21.2961 67.0973 21.7417 67.0973 22.2911H61.8372C61.8372 21.7417 62.2827 21.2961 62.8322 21.2961Z" fill="#CBD6DE"/>
<path d="M54.3315 87.6456C54.1013 87.6456 53.9149 87.4592 53.9149 87.2295V25.4595C53.9149 25.2293 54.1013 25.0434 54.3315 25.0434C54.5612 25.0434 54.7476 25.2293 54.7476 25.4595V87.2295C54.7476 87.4592 54.5612 87.6456 54.3315 87.6456Z" fill="#C2C2C3"/>
<path d="M51.5743 87.6456C51.3442 87.6456 51.1577 87.4592 51.1577 87.2295V25.4595C51.1577 25.2293 51.3442 25.0434 51.5743 25.0434C51.8044 25.0434 51.9908 25.2293 51.9908 25.4595V87.2295C51.9908 87.4592 51.8044 87.6456 51.5743 87.6456Z" fill="#C2C2C3"/>
<path d="M48.817 87.8552C48.5873 87.8552 48.4009 87.6692 48.4009 87.4391V25.4594C48.4009 25.2293 48.5873 25.0433 48.817 25.0433C49.0471 25.0433 49.2336 25.2293 49.2336 25.4594V87.4391C49.2336 87.6692 49.0471 87.8552 48.817 87.8552Z" fill="#C2C2C3"/>
<path d="M56.8411 22.5667C56.8411 22.5667 53.6518 5.06696 53.0327 2.70495C52.2955 2.23263 47.3926 1.52394 45.7714 1.52394H46.3494C44.7282 1.52394 39.8253 2.23263 39.0881 2.70495C38.4686 5.06696 35.2802 22.5667 35.2802 22.5667H56.8411Z" fill="#F3F3F4"/>
<path d="M55.0851 5.32483H56.4802C56.957 5.32483 57.3436 4.9377 57.3436 4.46092H54.2212C54.2212 4.9377 54.6083 5.32483 55.0851 5.32483Z" fill="white"/>
<path d="M60.9082 35.5256H58.7875C58.6715 35.5256 58.5774 35.4315 58.5774 35.316V25.4594C58.5774 25.3435 58.6715 25.2498 58.7875 25.2498H60.9082V25.6695H58.9971V35.106H60.9082V35.5256Z" fill="#F3F3F4"/>
<path d="M60.9082 35.5256H58.7875C58.6715 35.5256 58.5774 35.4315 58.5774 35.316V25.4594C58.5774 25.3435 58.6715 25.2498 58.7875 25.2498H60.9082V25.6695H58.9971V35.106H60.9082V35.5256Z" fill="white" fill-opacity="0.2"/>
<path d="M56.0904 91.8937H57.8949C58.1192 91.8937 58.3012 92.0757 58.3012 92.3V92.5239H55.6841V92.3C55.6841 92.0757 55.866 91.8937 56.0904 91.8937Z" fill="#CF0000"/>
<path d="M33.7535 35.3156H32.7999V25.4594H33.7535V35.3156Z" fill="#364258"/>
<path d="M33.7535 45.9674H32.7999V36.1117H33.7535V45.9674Z" fill="#364258"/>
<path d="M33.7535 56.6196H32.7999V46.7635H33.7535V56.6196Z" fill="#364258"/>
<path d="M33.7535 67.2715H32.7999V57.4153H33.7535V67.2715Z" fill="#364258"/>
<path d="M33.7535 77.9232H32.7999V68.0675H33.7535V77.9232Z" fill="#364258"/>
<path d="M33.7535 88.5751H32.7999V78.7189H33.7535V88.5751Z" fill="#364258"/>
<path d="M45.7549 90.5504V91.5035H35.8983V90.5504H45.7549Z" fill="#364258"/>
<path d="M59.5818 34.3804H60.9082V26.3947H59.5818V34.3804Z" fill="#364258"/>
<path d="M58.7874 45.9674H59.7404V36.1117H58.7874V45.9674Z" fill="#364258"/>
<path d="M58.7874 56.6196H59.7404V46.7635H58.7874V56.6196Z" fill="#364258"/>
<path d="M58.7874 67.2715H59.7404V57.4153H58.7874V67.2715Z" fill="#364258"/>
<path d="M58.7874 77.9232H59.7404V68.0675H58.7874V77.9232Z" fill="#364258"/>
<path d="M58.7874 88.5751H59.7404V78.7189H58.7874V88.5751Z" fill="#364258"/>
<path d="M46.3658 90.5504V91.5035H56.2224V90.5504H46.3658Z" fill="#364258"/>
<path d="M34.7771 18.7632C41.451 17.795 50.8246 17.795 57.4985 18.7632L57.9187 22.5667C51.3607 22.5582 40.9948 22.5756 34.2026 22.5667L34.7771 18.7632Z" fill="#364258"/>
<circle cx="64" cy="20" r="10.1667" fill="#006600" stroke="white"/>
<rect x="56.6666" y="18.6665" width="3.5" height="7.17" rx="1.75" fill="white"/>
<rect x="61.6666" y="16" width="3.5" height="9.83" rx="1.75" fill="white"/>
<rect x="66.6666" y="13.3333" width="3.5" height="12.5" rx="1.75" fill="white"/>
<g clip-path="url(#clip1_38_130361)">
<g filter="url(#filter1_ddd_38_130361)">
<g filter="url(#filter2_i_38_130361)">
<rect x="35.3334" y="63.3334" width="21.3333" height="21.3333" rx="10.6667" fill="#FF1010"/>
</g>
<rect x="35.8334" y="63.8334" width="20.3333" height="20.3333" rx="10.1667" stroke="#741646"/>
<g filter="url(#filter3_i_38_130361)">
<rect x="35.8334" y="63.8334" width="20.3333" height="20.3333" rx="10.1667" stroke="white"/>
</g>
<g filter="url(#filter4_f_38_130361)">
<ellipse cx="46" cy="68" rx="4" ry="2" fill="white"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_38_130361" x="-4" y="0" width="100" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.211765 0 0 0 0 0.258824 0 0 0 0 0.345098 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_38_130361"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_38_130361" result="shape"/>
</filter>
<filter id="filter1_ddd_38_130361" x="34.3334" y="63.3334" width="24.3334" height="24.3334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_38_130361"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_38_130361" result="effect2_dropShadow_38_130361"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_38_130361" result="effect3_dropShadow_38_130361"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_38_130361" result="shape"/>
</filter>
<filter id="filter2_i_38_130361" x="35.3334" y="60.3334" width="21.3334" height="24.3334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_130361"/>
</filter>
<filter id="filter3_i_38_130361" x="35.3334" y="60.3334" width="21.3334" height="24.3334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_130361"/>
</filter>
<filter id="filter4_f_38_130361" x="38.3" y="62.3" width="15.4" height="11.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.85" result="effect1_foregroundBlur_38_130361"/>
</filter>
<clipPath id="clip0_38_130361">
<rect width="92" height="92" fill="white" transform="translate(0 2)"/>
</clipPath>
<clipPath id="clip1_38_130361">
<rect width="24" height="24" fill="white" transform="translate(34 62)"/>
</clipPath>
</defs>
</svg>
