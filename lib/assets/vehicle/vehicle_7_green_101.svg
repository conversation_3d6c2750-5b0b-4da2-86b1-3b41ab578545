<svg width="92" height="92" viewBox="0 0 92 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_42_140732)">
<path d="M21.5 37C21.5 36.1716 22.1716 35.5 23 35.5H31.5C32.3284 35.5 33 36.1716 33 37V45C33 48.4478 34.3696 51.7544 36.8076 54.1924C39.2456 56.6304 42.5522 58 46 58C49.4478 58 52.7544 56.6304 55.1924 54.1924C57.6304 51.7544 59 48.4478 59 45V37C59 36.1716 59.6716 35.5 60.5 35.5H69C69.8284 35.5 70.5 36.1716 70.5 37V45C70.5 58.5314 59.5314 69.5 46 69.5C32.4686 69.5 21.5 58.5314 21.5 45L21.5 37Z" fill="#39B54A" stroke="white"/>
<rect x="21.5" y="23" width="11.5" height="9" rx="1.5" fill="#52C41A"/>
<rect x="21.5" y="23" width="11.5" height="9" rx="1.5" fill="white" fill-opacity="0.2"/>
<rect x="21.5" y="23" width="11.5" height="9" rx="1.5" stroke="white"/>
<rect x="59" y="23" width="11.5" height="9" rx="1.5" fill="#52C41A"/>
<rect x="59" y="23" width="11.5" height="9" rx="1.5" fill="white" fill-opacity="0.2"/>
<rect x="59" y="23" width="11.5" height="9" rx="1.5" stroke="white"/>
<path d="M42.5359 53.8031L42.536 53.8028C42.6178 53.6185 42.7997 53.5 43.002 53.5H50.979C51.3573 53.5 51.6045 53.8989 51.4347 54.2384L49.1657 58.7764L48.8039 59.5H49.613H52.971C53.4251 59.5 53.6527 60.0491 53.3314 60.3704L41.3784 72.3234C40.9964 72.7054 40.3641 72.3113 40.5342 71.8025L40.5343 71.8021L43.0823 64.1581L43.3017 63.5H42.608H39.01C39.01 63.5 39.01 63.5 39.01 63.5C38.925 63.5 38.8414 63.4788 38.7667 63.4382C38.6921 63.3977 38.6287 63.3392 38.5824 63.2679C38.5361 63.1967 38.5083 63.115 38.5016 63.0303C38.4949 62.9456 38.5094 62.8606 38.5439 62.783L42.5359 53.8031Z" fill="#EF3E36" stroke="white"/>
<g clip-path="url(#clip0_42_140732)">
<g filter="url(#filter1_ddd_42_140732)">
<g filter="url(#filter2_i_42_140732)">
<rect x="35.3333" y="61.3333" width="21.3333" height="21.3333" rx="10.6667" fill="#FF1010"/>
</g>
<rect x="35.8333" y="61.8333" width="20.3333" height="20.3333" rx="10.1667" stroke="#741646"/>
<g filter="url(#filter3_i_42_140732)">
<rect x="35.8333" y="61.8333" width="20.3333" height="20.3333" rx="10.1667" stroke="white"/>
</g>
<g filter="url(#filter4_f_42_140732)">
<ellipse cx="46" cy="66" rx="4" ry="2" fill="white"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_42_140732" x="-4" y="-2" width="100" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.211765 0 0 0 0 0.258824 0 0 0 0 0.345098 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_42_140732"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_42_140732" result="shape"/>
</filter>
<filter id="filter1_ddd_42_140732" x="34.3333" y="61.3333" width="24.3333" height="24.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_42_140732"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_42_140732" result="effect2_dropShadow_42_140732"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_42_140732" result="effect3_dropShadow_42_140732"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_42_140732" result="shape"/>
</filter>
<filter id="filter2_i_42_140732" x="35.3333" y="58.3333" width="21.3333" height="24.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_42_140732"/>
</filter>
<filter id="filter3_i_42_140732" x="35.3333" y="58.3333" width="21.3333" height="24.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_42_140732"/>
</filter>
<filter id="filter4_f_42_140732" x="38.3" y="60.3" width="15.4" height="11.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.85" result="effect1_foregroundBlur_42_140732"/>
</filter>
<clipPath id="clip0_42_140732">
<rect width="24" height="24" fill="white" transform="translate(34 60)"/>
</clipPath>
</defs>
</svg>
