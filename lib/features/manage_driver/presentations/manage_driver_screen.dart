import 'package:avema_v2/application/enums/avema_enums.dart';
import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/application/mixins/chip_scroll_mixin.dart';
import 'package:avema_v2/application/mixins/scroll_mixin.dart';
import 'package:avema_v2/application/utils/avema_dialog_util.dart';
import 'package:avema_v2/application/utils/avema_loading.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/manage_driver/application/manage_driver_bloc.dart';
import 'package:avema_v2/features/manage_driver/data/get_driver_request.dart';
import 'package:avema_v2/features/manage_driver/repository/driver_repository.dart';
import 'package:avema_v2/features/theme/avema_text_style.dart';
import 'package:avema_v2/uis/animated_button.dart';
import 'package:avema_v2/uis/avema_empty_view.dart';
import 'package:avema_v2/uis/avema_scaffold.dart';
import 'package:avema_v2/uis/avema_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'add_update_driver_screen.dart';
import 'widgets/chip_list_shimmer.dart';
import 'widgets/driver_list.dart';
import 'widgets/driver_sort_chip_list.dart';

class ManageDriverScreen extends StatefulWidget {
  const ManageDriverScreen({super.key});

  @override
  State<ManageDriverScreen> createState() => _ManageDriverScreenState();
}

class _ManageDriverScreenState extends State<ManageDriverScreen>
    with ChipScrollMixin, ScrollMixin {
  late ManageDriverBloc _bloc;
  late DriverRepository driverRepository;
  late TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    initChipScroll();
    initScroll(() => _bloc.add(LoadMoreDriverEvent()));
    _searchController = TextEditingController();
    _getData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    disposeScroll();
    disposeChipScroll();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final language = AVELanguages.instance;
    final theme = Theme.of(context).colorScheme;
    return BlocBuilder<ManageDriverBloc, ManageDriverState>(
      bloc: _bloc,
      builder: (context, state) {
        final itemCount = state.listDriverSorted?.length ?? 0;
        final length = itemCount > 0 ? "($itemCount)" : "";
        final titleAppbar = '${language.driver} $length';
        final isLoading = state.status == ManageDriverStatus.loading;
        final drivers = state.listDriverSorted ?? [];
        final driversEmpty = drivers.isEmpty == true;
        final cities = state.listCity;
        final plates = state.listPlate;
        return AvemaScaffold(
          title: titleAppbar,
          onAddTap: isLoading
              ? null
              : () => context.push(
                    AddUpdateDriverScreen(
                      bloc: _bloc,
                      isEdit: false,
                      listCity: cities,
                      listPlate: plates,
                    ),
                  ),
          onBackTap: () => _bloc.add(ResetStateEvent()),
          body: Column(
            children: [
              context.verticalSpaceL,
              Padding(
                padding: context.pdSymHorizontalL,
                child: AvemaTextFormField(
                  enabled: !isLoading,
                  contentPadding: context.pdSymHorizontalL,
                  labelText: language.search,
                  hintText: language.searchDriver,
                  controller: _searchController,
                  hintStyle: AvemaTextStyle.bodySmall.copyWith(
                    color: theme.onSurface.withOpacity(0.4),
                  ),
                  prefixIcon: const Icon(
                    Icons.search,
                    color: Colors.grey,
                  ),
                  suffixIcon: _searchController.text.isEmpty
                      ? const SizedBox()
                      : AvemaButton(
                          color: Colors.transparent,
                          onTap: onClearSearch,
                          child: const Icon(
                            Icons.clear,
                            color: Colors.grey,
                          ),
                        ),
                  onChanged: (text) {
                    _bloc.add(SearchDriverEvent(searchKey: text));
                  },
                ),
              ),
              context.verticalSpaceL,
              isLoading
                  ? const ChipListShimmer()
                  : DriverSortChipList(
                      chipController: chipController,
                      state: state,
                      bloc: _bloc,
                      scrollToSelectedChip: scrollToSelectedChip,
                      getChipKey: getChipKey,
                    ),
              if (!isLoading)
                Divider(
                  color: theme.onSurface.withOpacity(0.1),
                  thickness: 1,
                  endIndent: 16,
                  indent: 16,
                ),
              switch (state.status) {
                ManageDriverStatus.loading => const AvemaLoading(),
                ManageDriverStatus.success => driversEmpty
                    ? const AvemaEmptyView()
                    : DriverList(
                        scrollController: scrollController,
                        state: state,
                        imageUrl: state.driverPhoto,
                        onShowConfirmDelete: _showConfirmDelete,
                        onGetDriverDetail: (driverId) {
                          _bloc.add(GetDriverDetailEvent(id: driverId));
                        },
                        onNavigateToEdit: () => context.push(
                          AddUpdateDriverScreen(
                            bloc: _bloc,
                            isEdit: true,
                            isPhoto: state.driverDetail?.photo,
                            listCity: cities,
                            listPlate: plates,
                          ),
                        ),
                      ),
                ManageDriverStatus.failure => const AvemaEmptyView(),
                _ => const AvemaEmptyView(),
              }
            ],
          ),
        );
      },
    );
  }

  void onClearSearch() {
    _searchController.clear();
    _bloc.add(ClearSearchDriverEvent());
    FocusManager.instance.primaryFocus?.unfocus();
  }

  void _showConfirmDelete() {
    final language = AVELanguages.instance;
    AvemaDialogUtil.show(
      context,
      content: language.contentDeleteDriver,
    );
  }

  void _getData() {
    driverRepository = DriverRepository();
    _bloc = ManageDriverBloc(driverRepository);
    _bloc.add(
      GetListDriverEvent(
        request: const GetDriverRequest(
          page: '0',
          pageSize: '10',
        ),
      ),
    );
    _bloc.add(GetCityEvent());
    _bloc.add(GetPlateEvent());
  }
}
