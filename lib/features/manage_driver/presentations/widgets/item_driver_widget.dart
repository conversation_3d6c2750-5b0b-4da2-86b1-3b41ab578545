import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/features/manage_driver/data/driver_response.dart';
import 'package:avema_v2/features/manage_driver/presentations/widgets/circular_image.dart';
import 'package:avema_v2/features/theme/avema_theme.dart';
import 'package:avema_v2/features/theme/theme_bloc.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:flutter/material.dart';

import 'infor_driver_widget.dart';

class ItemDriverWidget extends StatefulWidget {
  final DriverData? driverResponse;
  final void Function()? onEdit;
  final void Function()? onDelete;
  const ItemDriverWidget({
    super.key,
    this.driverResponse,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<ItemDriverWidget> createState() => _ItemDriverWidgetState();
}

class _ItemDriverWidgetState extends State<ItemDriverWidget> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final isLightTheme = getIt<ThemeBloc>().state.isLightTheme();
    final driverResponse = widget.driverResponse;
    final driverId = driverResponse?.photo == true ? driverResponse?.id : null;

    return Container(
      padding: context.pdSymHorLVerML,
      decoration: BoxDecoration(
        color: theme.surface,
        borderRadius: BorderRadius.circular(8),
        boxShadow:
            isLightTheme ? AvemaTheme.lightBoxShadow : AvemaTheme.darkBoxShadow,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CircularImage(
            driverId:  driverId,
          ),
          context.horizontalSpaceML,
          Expanded(
            child: InforDriverWidget(
              driverResponse: widget.driverResponse,
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                padding: EdgeInsets.zero,
                onPressed: widget.onEdit,
                icon: Icon(
                  Icons.edit,
                  color: theme.onSurface.withOpacity(0.6),
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                onPressed: widget.onDelete,
                icon: const Icon(
                  Icons.delete,
                  color: AvemaColor.truckMoving,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
