import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/manage_driver/data/driver_response.dart';
import 'package:avema_v2/features/theme/avema_text_style.dart';
import 'package:flutter/material.dart';

class InforDriverWidget extends StatelessWidget {
  final DriverData? driverResponse;
  const InforDriverWidget({
    super.key,
    this.driverResponse,
  });

  @override
  Widget build(BuildContext context) {
    final language = AVELanguages.instance;
    final name = driverResponse?.name;
    final phone = driverResponse?.phone;
    final phone2 = driverResponse?.phone2;
    final rifd = driverResponse?.rfid;
    final licenseNo = driverResponse?.licenseNo;
    final driverNo = driverResponse?.driverNo;
    final city = driverResponse?.city;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextInfo(
          context,
          title: language.driver,
          content: name,
        ),
        if (city?.isNotEmpty == true) context.verticalSpaceS,
        _buildTextInfo(
          context,
          title: language.city,
          content: city,
        ),
        if (phone?.isNotEmpty == true) context.verticalSpaceS,
        _buildTextInfo(
          context,
          title: language.phone,
          content: phone,
        ),
        if (phone2?.isNotEmpty == true) ...{
          context.verticalSpaceS,
          _buildTextInfo(
            context,
            title: '${language.phone} 2',
            content: phone2,
          ),
        },
        if (rifd?.isNotEmpty == true) context.verticalSpaceS,
        _buildTextInfo(
          context,
          title: language.rfid,
          content: rifd,
        ),
        if (licenseNo?.isNotEmpty == true) context.verticalSpaceS,
        if (licenseNo?.isNotEmpty == true)
          _buildTextInfo(
            context,
            title: language.licenseNo,
            content: licenseNo,
          ),
        if (driverNo?.isNotEmpty == true) context.verticalSpaceS,
        if (driverNo?.isNotEmpty == true)
          _buildTextInfo(
            context,
            title: language.driverNo,
            content: driverNo,
          ),
      ],
    );
  }

  Widget _buildTextInfo(
    BuildContext context, {
    required String title,
    String? content,
  }) {
    final theme = Theme.of(context).colorScheme;
    if (content?.isEmpty == true) {
      return const SizedBox.shrink();
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$title: ',
          style: AvemaTextStyle.bodySmall.copyWith(
            color: theme.onSurface,
          ),
        ),
        Expanded(
          child: Text(
            content!,
            style: AvemaTextStyle.bodyLarge.copyWith(
              color: theme.onSurface,
            ),
          ),
        ),
      ],
    );
  }
}
