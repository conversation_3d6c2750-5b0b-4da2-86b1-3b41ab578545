import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/assets_manager/assets_manager.dart';
import 'package:flutter/material.dart';

class CustomImage extends StatelessWidget {
  final String? imageUrl;
  final double? size;
  final double? iconSize;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final Color? colorIcon;
  const CustomImage({
    super.key,
    this.imageUrl,
    this.size,
    this.padding,
    this.color,
    this.colorIcon,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    const defaultSize = 80.0;
    final defaultIconSize = iconSize ?? 48.0;
    if (imageUrl == null) {
      return Image.asset(
        height: defaultIconSize,
        width: defaultIconSize,
        assetsGen.assets.iconUser.path,
        color: colorIcon ?? theme.onSurface.withOpacity(0.6),
      );
    }
    return Container(
      height: size ?? defaultSize,
      width: size ?? defaultSize,
      padding: padding ?? context.paddingAllL,
      decoration: BoxDecoration(
        color: color ?? theme.onPrimary,
        shape: BoxShape.circle,
      ),
      child: Image.asset(
        imageUrl!,
        color: colorIcon ?? theme.onSurface.withOpacity(0.6),
      ),
    );
  }
}
