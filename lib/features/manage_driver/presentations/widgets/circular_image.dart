import 'dart:io';

import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/application/injection_module.dart';
import 'package:avema_v2/assets_manager/assets_manager.dart';
import 'package:avema_v2/features/login/application/auth_bloc.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:avema_v2/uis/animated_button.dart';
import 'package:avema_v2/uis/error_image_view.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

import 'custom_image.dart';

class CircularImage extends StatelessWidget {
  final String? driverId;
  final double size;
  final bool? isEdit;
  final VoidCallback? onTap;
  final XFile? imagePicked;

  const CircularImage({
    super.key,
    this.driverId,
    this.size = 50,
    this.isEdit,
    this.onTap,
    this.imagePicked,
  });

  bool get _isEditable => isEdit != null;

  @override
  Widget build(BuildContext context) {
    return AvemaButton(
      onTap: _isEditable ? onTap : null,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          _buildMainImage(),
          _buildEditIcon(context),
        ],
      ),
    );
  }

  Widget _buildMainImage() {
    if (imagePicked != null) {
      return _buildCircularImage(
        child: Image.file(
          File(imagePicked!.path),
          fit: BoxFit.cover,
          width: size,
          height: size,
        ),
      );
    }
    final accessToken = getIt<AuthBloc>().state.maybeMap(
          orElse: () => '',
          authenticated: (auth) => auth.user.accessToken,
        );
    if (driverId != null && isEdit != false && accessToken.isNotEmpty) {
      return _buildCircularImage(
        child: Image.network(
          "${AvemaAPI.baseUrl}/v1/driver/getphoto/$driverId",
          fit: BoxFit.cover,
          width: size,
          height: size,
          errorBuilder: (_, __, ___) => ErrorImageView(size: size),
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return SizedBox(
              height: size - context.valueL,
              width: size - context.valueL,
              child: CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
            );
          },
          headers: {
            'Authorization': "Bearer $accessToken",
            'Content-Type': 'application/json',
          },
        ),
      );
    }

    return CustomImage(
      iconSize: size,
    );
  }

  Widget _buildCircularImage({
    required Widget child,
  }) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(size / 2),
      child: child,
    );
  }

  Widget _buildEditIcon(BuildContext context) {
    if (isEdit == null) return const SizedBox.shrink();

    final theme = Theme.of(context).colorScheme;
    final iconPath = isEdit == true
        ? assetsGen.assets.iconEditImage.path
        : assetsGen.assets.iconAddImage.path;

    return Positioned(
      right: 0,
      bottom: 0,
      child: CustomImage(
        imageUrl: iconPath,
        size: 24,
        padding: context.paddingAllS,
        color: theme.primary,
        colorIcon: theme.surface,
      ),
    );
  }
}
