import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/application/utils/avema_loading.dart';
import 'package:avema_v2/features/manage_driver/application/manage_driver_bloc.dart';
import 'package:flutter/material.dart';

import 'item_driver_widget.dart';

class DriverList extends StatelessWidget {
  final ScrollController scrollController;
  final ManageDriverState state;
  final String? imageUrl;
  final VoidCallback onShowConfirmDelete;
  final Function(String driverId) onGetDriverDetail;
  final Function()? onNavigateToEdit;

  const DriverList({
    super.key,
    required this.scrollController,
    required this.state,
    this.imageUrl,
    required this.onShowConfirmDelete,
    required this.onGetDriverDetail,
    this.onNavigateToEdit,
  });

  @override
  Widget build(BuildContext context) {
    final itemCount = state.listDriverSorted?.length ?? 0;
    const botHeight = kBottomNavigationBarHeight;

    return Expanded(
      child: ListView.separated(
        controller: scrollController,
        padding: context.pdSymHorizontalL.copyWith(top: 16),
        itemCount: itemCount + (state.isLoadingMore ? 2 : 1),
        separatorBuilder: (_, __) => context.verticalSpaceML,
        itemBuilder: (context, index) {
          if (index == itemCount) {
            if (state.isLoadingMore) {
              return const AvemaLoading();
            }
            return context.verticalSpace(botHeight);
          }
          if (index > itemCount) {
            return context.verticalSpace(botHeight);
          }
          final driver = state.listDriverSorted![index];

          return ItemDriverWidget(
            driverResponse: driver,
            onEdit: () {
              onGetDriverDetail(driver.id);
              onNavigateToEdit?.call();
            },
            onDelete: onShowConfirmDelete,
          );
        },
      ),
    );
  }
}
