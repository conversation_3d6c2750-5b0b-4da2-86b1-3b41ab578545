import 'package:avema_v2/application/enums/avema_enums.dart';
import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/application/utils/avema_helper.dart';
import 'package:avema_v2/application/utils/avema_image_picker_util.dart';
import 'package:avema_v2/application/utils/avema_loading.dart';
import 'package:avema_v2/application/utils/date_time_extension.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/manage_driver/application/manage_driver_bloc.dart';
import 'package:avema_v2/features/manage_driver/data/city_response.dart';
import 'package:avema_v2/features/manage_driver/data/driver_detail_reponse.dart';
import 'package:avema_v2/features/manage_driver/data/plate_response.dart';
import 'package:avema_v2/features/manage_driver/presentations/widgets/circular_image.dart';
import 'package:avema_v2/features/theme/avema_text_style.dart';
import 'package:avema_v2/features/theme/avema_theme.dart';
import 'package:avema_v2/uis/animated_button.dart';
import 'package:avema_v2/uis/avema_checkbox.dart';
import 'package:avema_v2/uis/avema_scaffold.dart';
import 'package:avema_v2/uis/avema_text_form_field.dart';
import 'package:avema_v2/uis/dynamic_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';


class AddUpdateDriverScreen extends StatefulWidget {
  final bool? isPhoto;
  final bool? isEdit;
  final List<CityResponse>? listCity;
  final List<PlateResponse>? listPlate;
  final ManageDriverBloc bloc;
  const AddUpdateDriverScreen({
    super.key,
    this.isPhoto,
    this.isEdit,
    this.listCity,
    this.listPlate,
    required this.bloc,
  });

  @override
  State<AddUpdateDriverScreen> createState() => _AddUpdateDriverScreenState();
}

class _AddUpdateDriverScreenState extends State<AddUpdateDriverScreen> {
  late TextEditingController _cityController;
  late TextEditingController _driverNoController;
  late TextEditingController _driverController;
  late TextEditingController _phoneController;
  late TextEditingController _phone2Controller;
  late TextEditingController _plateController;
  late TextEditingController _rfidController;
  late TextEditingController _licenseNoController;
  late TextEditingController _registerPlaceController;
  late TextEditingController _registerDateController;
  late TextEditingController _expiredDateController;
  late TextEditingController _usernameController;
  late TextEditingController _passwordController;

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _initTextController();
    _initData();
  }

  @override
  void dispose() {
    _disposeTextController();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final language = AVELanguages.instance;
    final theme = Theme.of(context).colorScheme;
    final listCity = widget.listCity?.listName ?? [];
    final listPlate = widget.listPlate?.listLabel ?? [];
    return BlocConsumer<ManageDriverBloc, ManageDriverState>(
      bloc: widget.bloc,
      listener: (context, state) {
        final status = state.detailDriverStatus;
        final isSuccess = status == DetailDriverStatus.success;
        if (isSuccess) {
          _initData(
            driverDetail: state.driverDetail,
          );
          widget.bloc.add(ClearDetailDriverEvent());
        }
      },
      builder: (context, state) {
        final loadingState =
            state.detailDriverStatus == DetailDriverStatus.loading;
        return AvemaScaffold(
          title: widget.isEdit == true
              ? language.updateDriver
              : language.addDriver,
          onBackTap: () => widget.bloc.add(ClearImageEvent()),
          action: const SizedBox.shrink(),
          floatingActionButton: !loadingState
              ? Padding(
                  padding: context.pdSymHorizontalL.copyWith(
                    bottom: context.valueL,
                  ),
                  child: AvemaButton(
                    onTap: _onSubmit,
                    padding: context.paddingAllML,
                    width: context.width,
                    color: theme.primary,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          widget.isEdit == true ? Icons.edit : Icons.add,
                          color: AvemaColor.white,
                        ),
                        context.horizontalSpaceM,
                        Text(
                          widget.isEdit == true
                              ? language.update
                              : language.addDriver,
                          textAlign: TextAlign.center,
                          style: AvemaTextStyle.lableLagre.copyWith(
                            color: AvemaColor.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : null,
          body: loadingState
              ? const Center(
                  child: AvemaLoading(),
                )
              : GestureDetector(
                  onTap: () => context.unfocus(),
                  child: SingleChildScrollView(
                    child: Form(
                      key: _formKey,
                      child: Padding(
                        padding: context.pdSymHorizontalL,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            context.verticalSpaceL,

                            //Image
                            Center(
                              child: CircularImage(
                                isEdit: widget.isEdit,
                                driverId: state.driverDetail?.id,
                                imagePicked: state.imagePicked,
                                size: 80,
                                onTap: _pickImage,
                              ),
                            ),
                            context.verticalSpaceXL,

                            //Activated
                            Container(
                              padding: const EdgeInsets.only(left: 8),
                              decoration: BoxDecoration(
                                color: theme.surface,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: theme.secondary,
                                ),
                              ),
                              child: AvemaCheckbox(
                                label: language.isActivated,
                                value: state.isActivated ?? false,
                                onChanged: (value) {
                                  widget.bloc.add(
                                    UpdateActivated(
                                      id: state.driverDetail?.id,
                                      isActivated: value,
                                    ),
                                  );
                                },
                              ),
                            ),
                            context.verticalSpaceXL,

                            // Driver Information
                            Text(
                              language.driverInformation,
                              style: AvemaTextStyle.lableLagre.copyWith(
                                color: theme.onSurface,
                              ),
                            ),
                            context.verticalSpaceM,

                            // City
                            AvemaTextFormField(
                              isReadOnly: true,
                              isRequired: true,
                              labelText: language.city,
                              controller: _cityController,
                              onValidator: _onValidator,
                              suffixIcon: _buildIconRight(),
                              onTap: () => _navigateToDynamicList(
                                title: language.city,
                                data: listCity,
                                controller: _cityController,
                              ),
                            ),
                            context.verticalSpaceL,

                            // Driver No
                            AvemaTextFormField(
                              labelText: language.driverNo,
                              controller: _driverNoController,
                            ),
                            context.verticalSpaceL,

                            // Driver Name
                            AvemaTextFormField(
                              isRequired: true,
                              labelText: language.driver,
                              controller: _driverController,
                              onValidator: _onValidator,
                            ),
                            context.verticalSpaceL,

                            // Phone
                            AvemaTextFormField(
                              labelText: language.phone,
                              controller: _phoneController,
                            ),
                            context.verticalSpaceL,

                            // Phone 2
                            AvemaTextFormField(
                              labelText: '${language.phone} 2',
                              controller: _phone2Controller,
                            ),
                            context.verticalSpaceL,
                            Text(
                              language.licenseInformation,
                              style: AvemaTextStyle.lableLagre.copyWith(
                                color: theme.onSurface,
                              ),
                            ),
                            context.verticalSpaceM,

                            // Plate
                            AvemaTextFormField(
                              isReadOnly: true,
                              labelText: language.plate,
                              controller: _plateController,
                              suffixIcon: _buildIconRight(),
                              onTap: () => _navigateToDynamicList(
                                title: language.plate,
                                data: listPlate,
                                controller: _plateController,
                              ),
                            ),
                            context.verticalSpaceL,

                            // RFID
                            AvemaTextFormField(
                              isReadOnly: widget.isEdit == true,
                              labelText: language.rfid,
                              controller: _rfidController,
                            ),
                            context.verticalSpaceL,

                            // License No
                            AvemaTextFormField(
                              labelText: language.licenseNo,
                              controller: _licenseNoController,
                            ),
                            context.verticalSpaceL,

                            // Register Place
                            AvemaTextFormField(
                              isReadOnly: true,
                              labelText: language.registerPlace,
                              controller: _registerPlaceController,
                              suffixIcon: _buildIconRight(),
                              onTap: () => _navigateToDynamicList(
                                title: language.registerPlace,
                                data: listCity,
                                controller: _registerPlaceController,
                              ),
                            ),
                            context.verticalSpaceL,

                            // Register Date
                            AvemaTextFormField(
                              isReadOnly: true,
                              labelText: language.registerDate,
                              controller: _registerDateController,
                              suffixIcon: _buildCalendarIcon(),
                              onTap: _showRegisterDate,
                            ),
                            context.verticalSpaceL,

                            // Expired Date
                            AvemaTextFormField(
                              isReadOnly: true,
                              labelText: language.expiredDate,
                              controller: _expiredDateController,
                              suffixIcon: _buildCalendarIcon(),
                              onTap: _showExpiredDate,
                            ),
                            context.verticalSpace(
                              kBottomNavigationBarHeight * 2.5,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
        );
      },
    );
  }

  Future<void> _navigateToDynamicList({
    required String title,
    required List<String> data,
    required TextEditingController controller,
  }) async {
    final result = await context.push(
      DynamicList(
        title: title,
        data: data,
        selectedData: controller.text,
      ),
    );
    if (result != null) {
      controller.text = result;
    }
  }

  void _pickImage() async {
    final image = await ImagePickerUtil.pickFromGallery();
    if (image != null) {
      widget.bloc.add(PickImageEvent(image: image));
    }
  }

  String? _onValidator(String? value) {
    if (value == null || value.isEmpty) {
      return AVELanguages.instance.valueIsRequired;
    }
    return null;
  }

  Widget _buildIconRight() {
    final theme = Theme.of(context).colorScheme;
    return Icon(
      Icons.arrow_forward_ios_outlined,
      size: 16,
      color: theme.onSurface.withOpacity(0.5),
    );
  }

  Widget _buildCalendarIcon() {
    return const Icon(
      Icons.calendar_month,
    );
  }

  void _initTextController() {
    _cityController = TextEditingController();
    _driverNoController = TextEditingController();
    _driverController = TextEditingController();
    _phoneController = TextEditingController();
    _phone2Controller = TextEditingController();
    _plateController = TextEditingController();
    _rfidController = TextEditingController();
    _licenseNoController = TextEditingController();
    _registerPlaceController = TextEditingController();
    _registerDateController = TextEditingController();
    _expiredDateController = TextEditingController();
    _usernameController = TextEditingController();
    _passwordController = TextEditingController();
  }

  void _initData({
    DriverDetailResponse? driverDetail,
  }) {
    if (driverDetail != null) {
      final driver = driverDetail;

      final city = widget.listCity?.getCityById(driver.cityId);
      final plate = widget.listPlate?.getPlateByValue(driver.vehicleId);
      final registerPlace = widget.listCity?.getCityById(driver.registerPlace);
      final registerDate = driver.registerDate?.formatFromMilliseconds() ?? '';
      final expiredDate = driver.expiredDate?.formatFromMilliseconds() ?? '';
      _cityController.text = city?.name ?? '';
      _driverNoController.text = driver.driverNo ?? '';
      _driverController.text = driver.name ?? '';
      _phoneController.text = driver.phone ?? '';
      _phone2Controller.text = driver.phone2 ?? '';
      _rfidController.text = driver.rfid ?? '';
      _plateController.text = plate?.label ?? '';
      _licenseNoController.text = driver.licenseNo ?? '';
      _registerPlaceController.text = registerPlace?.name ?? '';
      _registerDateController.text = registerDate;
      _expiredDateController.text = expiredDate;
      _usernameController.text = driver.username ?? '';
    }
    widget.bloc.add(
      InitActivated(
        isActivated: driverDetail?.isActivated ?? false,
      ),
    );
  }

  void _disposeTextController() {
    _cityController.dispose();
    _driverNoController.dispose();
    _driverController.dispose();
    _phoneController.dispose();
    _phone2Controller.dispose();
    _plateController.dispose();
    _rfidController.dispose();
    _licenseNoController.dispose();
    _registerPlaceController.dispose();
    _registerDateController.dispose();
    _expiredDateController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
  }

  void _onSubmit() {
    FocusScope.of(context).unfocus();
    final bool isValid = _formKey.currentState!.validate();
    if (isValid) {}
  }

  void _showRegisterDate() {
    AvemaHelper.selectDate(
      context,
      controller: _registerDateController,
    );
  }

  void _showExpiredDate() {
    AvemaHelper.selectDate(
      context,
      controller: _expiredDateController,
    );
  }
}
