// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'city_response.freezed.dart';
part 'city_response.g.dart';

@freezed
class CityResponse with _$CityResponse {
  const factory CityResponse({
    @Json<PERSON>ey(name: 'Id') @Default("") String id,
    @JsonKey(name: 'Name') @Default("") String name,
  }) = _CityResponse;

  factory CityResponse.fromJson(Map<String, dynamic> json) =>
      _$CityResponseFromJson(json);

  const CityResponse._();
}

extension CityResponseEx on List<CityResponse>? {
  List<String>? get listName {
    if (this?.isEmpty == true) return [];
    return this!.map((e) => e.name).toList();
  }

  CityResponse? getCityById(String? id) {
    if (this?.isEmpty == true) return null;
    return this!.firstWhere(
      (element) => element.id == id,
      orElse: () => const CityResponse(id: "", name: ""),
    );
  }

  CityResponse? getCityByName(String? name) {
    if (this?.isEmpty == true) return null;
    return this!.firstWhere(
      (element) => element.name == name,
      orElse: () => const CityResponse(id: "", name: ""),
    );
  }
}
