// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'driver_detail_reponse.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DriverDetailResponseImpl _$$DriverDetailResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$DriverDetailResponseImpl(
      id: json['Id'] as String,
      cityId: json['CityId'] as String?,
      rfid: json['RFID'] as String?,
      name: json['Name'] as String?,
      phone: json['Phone'] as String?,
      phone2: json['Phone2'] as String?,
      address: json['Address'] as String?,
      driverNo: json['DriverNo'] as String?,
      email: json['Email'] as String?,
      licenseNo: json['LicenseNo'] as String?,
      registerPlace: json['RegisterPlace'] as String?,
      username: json['Username'] as String?,
      isActivated: json['IsActivated'] as bool? ?? false,
      registerDate: (json['RegisterDate'] as num?)?.toInt(),
      expiredDate: (json['ExpiredDate'] as num?)?.toInt(),
      photo: json['Photo'] as bool? ?? false,
      fields: json['Fields'] as Map<String, dynamic>? ?? const {},
      vehicleId: json['VehicleId'] as String?,
    );

Map<String, dynamic> _$$DriverDetailResponseImplToJson(
        _$DriverDetailResponseImpl instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'CityId': instance.cityId,
      'RFID': instance.rfid,
      'Name': instance.name,
      'Phone': instance.phone,
      'Phone2': instance.phone2,
      'Address': instance.address,
      'DriverNo': instance.driverNo,
      'Email': instance.email,
      'LicenseNo': instance.licenseNo,
      'RegisterPlace': instance.registerPlace,
      'Username': instance.username,
      'IsActivated': instance.isActivated,
      'RegisterDate': instance.registerDate,
      'ExpiredDate': instance.expiredDate,
      'Photo': instance.photo,
      'Fields': instance.fields,
      'VehicleId': instance.vehicleId,
    };
