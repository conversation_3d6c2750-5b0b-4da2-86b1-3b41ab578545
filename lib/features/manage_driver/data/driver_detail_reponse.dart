// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'driver_detail_reponse.freezed.dart';
part 'driver_detail_reponse.g.dart';

@freezed
class DriverDetailResponse with _$DriverDetailResponse {
  const factory DriverDetailResponse({
    @Json<PERSON><PERSON>(name: 'Id') required String id,
    @J<PERSON><PERSON><PERSON>(name: 'CityId') String? cityId,
    @Json<PERSON><PERSON>(name: 'RFID') String? rfid,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Name') String? name,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Phone') String? phone,
    @<PERSON>son<PERSON><PERSON>(name: 'Phone2') String? phone2,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Address') String? address,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'DriverNo') String? driverNo,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Email') String? email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'LicenseNo') String? licenseNo,
    @<PERSON>son<PERSON>ey(name: 'RegisterPlace') String? registerPlace,
    @<PERSON><PERSON><PERSON><PERSON>(name: '<PERSON>rna<PERSON>') String? username,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'IsActivated') @Default(false) bool isActivated,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'RegisterDate') int? registerDate,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'ExpiredDate') int? expiredDate,
    @JsonKey(name: 'Photo') @Default(false) bool photo,
    @JsonKey(name: 'Fields') @Default({}) Map<String, dynamic> fields,
    @JsonKey(name: 'VehicleId') String? vehicleId,
  }) = _DriverDetailResponse;

  factory DriverDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$DriverDetailResponseFromJson(json);

  const DriverDetailResponse._();
}
