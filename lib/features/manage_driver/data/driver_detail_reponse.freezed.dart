// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_detail_reponse.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DriverDetailResponse _$DriverDetailResponseFromJson(Map<String, dynamic> json) {
  return _DriverDetailResponse.fromJson(json);
}

/// @nodoc
mixin _$DriverDetailResponse {
  @JsonKey(name: 'Id')
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'CityId')
  String? get cityId => throw _privateConstructorUsedError;
  @JsonKey(name: 'RFID')
  String? get rfid => throw _privateConstructorUsedError;
  @JsonKey(name: 'Name')
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'Phone')
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'Phone2')
  String? get phone2 => throw _privateConstructorUsedError;
  @JsonKey(name: 'Address')
  String? get address => throw _privateConstructorUsedError;
  @JsonKey(name: 'DriverNo')
  String? get driverNo => throw _privateConstructorUsedError;
  @JsonKey(name: 'Email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'LicenseNo')
  String? get licenseNo => throw _privateConstructorUsedError;
  @JsonKey(name: 'RegisterPlace')
  String? get registerPlace => throw _privateConstructorUsedError;
  @JsonKey(name: 'Username')
  String? get username => throw _privateConstructorUsedError;
  @JsonKey(name: 'IsActivated')
  bool get isActivated => throw _privateConstructorUsedError;
  @JsonKey(name: 'RegisterDate')
  int? get registerDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'ExpiredDate')
  int? get expiredDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'Photo')
  bool get photo => throw _privateConstructorUsedError;
  @JsonKey(name: 'Fields')
  Map<String, dynamic> get fields => throw _privateConstructorUsedError;
  @JsonKey(name: 'VehicleId')
  String? get vehicleId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DriverDetailResponseCopyWith<DriverDetailResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverDetailResponseCopyWith<$Res> {
  factory $DriverDetailResponseCopyWith(DriverDetailResponse value,
          $Res Function(DriverDetailResponse) then) =
      _$DriverDetailResponseCopyWithImpl<$Res, DriverDetailResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String id,
      @JsonKey(name: 'CityId') String? cityId,
      @JsonKey(name: 'RFID') String? rfid,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'Phone2') String? phone2,
      @JsonKey(name: 'Address') String? address,
      @JsonKey(name: 'DriverNo') String? driverNo,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'LicenseNo') String? licenseNo,
      @JsonKey(name: 'RegisterPlace') String? registerPlace,
      @JsonKey(name: 'Username') String? username,
      @JsonKey(name: 'IsActivated') bool isActivated,
      @JsonKey(name: 'RegisterDate') int? registerDate,
      @JsonKey(name: 'ExpiredDate') int? expiredDate,
      @JsonKey(name: 'Photo') bool photo,
      @JsonKey(name: 'Fields') Map<String, dynamic> fields,
      @JsonKey(name: 'VehicleId') String? vehicleId});
}

/// @nodoc
class _$DriverDetailResponseCopyWithImpl<$Res,
        $Val extends DriverDetailResponse>
    implements $DriverDetailResponseCopyWith<$Res> {
  _$DriverDetailResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cityId = freezed,
    Object? rfid = freezed,
    Object? name = freezed,
    Object? phone = freezed,
    Object? phone2 = freezed,
    Object? address = freezed,
    Object? driverNo = freezed,
    Object? email = freezed,
    Object? licenseNo = freezed,
    Object? registerPlace = freezed,
    Object? username = freezed,
    Object? isActivated = null,
    Object? registerDate = freezed,
    Object? expiredDate = freezed,
    Object? photo = null,
    Object? fields = null,
    Object? vehicleId = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cityId: freezed == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as String?,
      rfid: freezed == rfid
          ? _value.rfid
          : rfid // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      registerPlace: freezed == registerPlace
          ? _value.registerPlace
          : registerPlace // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      isActivated: null == isActivated
          ? _value.isActivated
          : isActivated // ignore: cast_nullable_to_non_nullable
              as bool,
      registerDate: freezed == registerDate
          ? _value.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as int?,
      expiredDate: freezed == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as int?,
      photo: null == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as bool,
      fields: null == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverDetailResponseImplCopyWith<$Res>
    implements $DriverDetailResponseCopyWith<$Res> {
  factory _$$DriverDetailResponseImplCopyWith(_$DriverDetailResponseImpl value,
          $Res Function(_$DriverDetailResponseImpl) then) =
      __$$DriverDetailResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String id,
      @JsonKey(name: 'CityId') String? cityId,
      @JsonKey(name: 'RFID') String? rfid,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'Phone2') String? phone2,
      @JsonKey(name: 'Address') String? address,
      @JsonKey(name: 'DriverNo') String? driverNo,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'LicenseNo') String? licenseNo,
      @JsonKey(name: 'RegisterPlace') String? registerPlace,
      @JsonKey(name: 'Username') String? username,
      @JsonKey(name: 'IsActivated') bool isActivated,
      @JsonKey(name: 'RegisterDate') int? registerDate,
      @JsonKey(name: 'ExpiredDate') int? expiredDate,
      @JsonKey(name: 'Photo') bool photo,
      @JsonKey(name: 'Fields') Map<String, dynamic> fields,
      @JsonKey(name: 'VehicleId') String? vehicleId});
}

/// @nodoc
class __$$DriverDetailResponseImplCopyWithImpl<$Res>
    extends _$DriverDetailResponseCopyWithImpl<$Res, _$DriverDetailResponseImpl>
    implements _$$DriverDetailResponseImplCopyWith<$Res> {
  __$$DriverDetailResponseImplCopyWithImpl(_$DriverDetailResponseImpl _value,
      $Res Function(_$DriverDetailResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cityId = freezed,
    Object? rfid = freezed,
    Object? name = freezed,
    Object? phone = freezed,
    Object? phone2 = freezed,
    Object? address = freezed,
    Object? driverNo = freezed,
    Object? email = freezed,
    Object? licenseNo = freezed,
    Object? registerPlace = freezed,
    Object? username = freezed,
    Object? isActivated = null,
    Object? registerDate = freezed,
    Object? expiredDate = freezed,
    Object? photo = null,
    Object? fields = null,
    Object? vehicleId = freezed,
  }) {
    return _then(_$DriverDetailResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cityId: freezed == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as String?,
      rfid: freezed == rfid
          ? _value.rfid
          : rfid // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      phone2: freezed == phone2
          ? _value.phone2
          : phone2 // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      driverNo: freezed == driverNo
          ? _value.driverNo
          : driverNo // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseNo: freezed == licenseNo
          ? _value.licenseNo
          : licenseNo // ignore: cast_nullable_to_non_nullable
              as String?,
      registerPlace: freezed == registerPlace
          ? _value.registerPlace
          : registerPlace // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      isActivated: null == isActivated
          ? _value.isActivated
          : isActivated // ignore: cast_nullable_to_non_nullable
              as bool,
      registerDate: freezed == registerDate
          ? _value.registerDate
          : registerDate // ignore: cast_nullable_to_non_nullable
              as int?,
      expiredDate: freezed == expiredDate
          ? _value.expiredDate
          : expiredDate // ignore: cast_nullable_to_non_nullable
              as int?,
      photo: null == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as bool,
      fields: null == fields
          ? _value._fields
          : fields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      vehicleId: freezed == vehicleId
          ? _value.vehicleId
          : vehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverDetailResponseImpl extends _DriverDetailResponse {
  const _$DriverDetailResponseImpl(
      {@JsonKey(name: 'Id') required this.id,
      @JsonKey(name: 'CityId') this.cityId,
      @JsonKey(name: 'RFID') this.rfid,
      @JsonKey(name: 'Name') this.name,
      @JsonKey(name: 'Phone') this.phone,
      @JsonKey(name: 'Phone2') this.phone2,
      @JsonKey(name: 'Address') this.address,
      @JsonKey(name: 'DriverNo') this.driverNo,
      @JsonKey(name: 'Email') this.email,
      @JsonKey(name: 'LicenseNo') this.licenseNo,
      @JsonKey(name: 'RegisterPlace') this.registerPlace,
      @JsonKey(name: 'Username') this.username,
      @JsonKey(name: 'IsActivated') this.isActivated = false,
      @JsonKey(name: 'RegisterDate') this.registerDate,
      @JsonKey(name: 'ExpiredDate') this.expiredDate,
      @JsonKey(name: 'Photo') this.photo = false,
      @JsonKey(name: 'Fields') final Map<String, dynamic> fields = const {},
      @JsonKey(name: 'VehicleId') this.vehicleId})
      : _fields = fields,
        super._();

  factory _$DriverDetailResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverDetailResponseImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String id;
  @override
  @JsonKey(name: 'CityId')
  final String? cityId;
  @override
  @JsonKey(name: 'RFID')
  final String? rfid;
  @override
  @JsonKey(name: 'Name')
  final String? name;
  @override
  @JsonKey(name: 'Phone')
  final String? phone;
  @override
  @JsonKey(name: 'Phone2')
  final String? phone2;
  @override
  @JsonKey(name: 'Address')
  final String? address;
  @override
  @JsonKey(name: 'DriverNo')
  final String? driverNo;
  @override
  @JsonKey(name: 'Email')
  final String? email;
  @override
  @JsonKey(name: 'LicenseNo')
  final String? licenseNo;
  @override
  @JsonKey(name: 'RegisterPlace')
  final String? registerPlace;
  @override
  @JsonKey(name: 'Username')
  final String? username;
  @override
  @JsonKey(name: 'IsActivated')
  final bool isActivated;
  @override
  @JsonKey(name: 'RegisterDate')
  final int? registerDate;
  @override
  @JsonKey(name: 'ExpiredDate')
  final int? expiredDate;
  @override
  @JsonKey(name: 'Photo')
  final bool photo;
  final Map<String, dynamic> _fields;
  @override
  @JsonKey(name: 'Fields')
  Map<String, dynamic> get fields {
    if (_fields is EqualUnmodifiableMapView) return _fields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fields);
  }

  @override
  @JsonKey(name: 'VehicleId')
  final String? vehicleId;

  @override
  String toString() {
    return 'DriverDetailResponse(id: $id, cityId: $cityId, rfid: $rfid, name: $name, phone: $phone, phone2: $phone2, address: $address, driverNo: $driverNo, email: $email, licenseNo: $licenseNo, registerPlace: $registerPlace, username: $username, isActivated: $isActivated, registerDate: $registerDate, expiredDate: $expiredDate, photo: $photo, fields: $fields, vehicleId: $vehicleId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverDetailResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cityId, cityId) || other.cityId == cityId) &&
            (identical(other.rfid, rfid) || other.rfid == rfid) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.phone2, phone2) || other.phone2 == phone2) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.driverNo, driverNo) ||
                other.driverNo == driverNo) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.licenseNo, licenseNo) ||
                other.licenseNo == licenseNo) &&
            (identical(other.registerPlace, registerPlace) ||
                other.registerPlace == registerPlace) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.isActivated, isActivated) ||
                other.isActivated == isActivated) &&
            (identical(other.registerDate, registerDate) ||
                other.registerDate == registerDate) &&
            (identical(other.expiredDate, expiredDate) ||
                other.expiredDate == expiredDate) &&
            (identical(other.photo, photo) || other.photo == photo) &&
            const DeepCollectionEquality().equals(other._fields, _fields) &&
            (identical(other.vehicleId, vehicleId) ||
                other.vehicleId == vehicleId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      cityId,
      rfid,
      name,
      phone,
      phone2,
      address,
      driverNo,
      email,
      licenseNo,
      registerPlace,
      username,
      isActivated,
      registerDate,
      expiredDate,
      photo,
      const DeepCollectionEquality().hash(_fields),
      vehicleId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverDetailResponseImplCopyWith<_$DriverDetailResponseImpl>
      get copyWith =>
          __$$DriverDetailResponseImplCopyWithImpl<_$DriverDetailResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverDetailResponseImplToJson(
      this,
    );
  }
}

abstract class _DriverDetailResponse extends DriverDetailResponse {
  const factory _DriverDetailResponse(
          {@JsonKey(name: 'Id') required final String id,
          @JsonKey(name: 'CityId') final String? cityId,
          @JsonKey(name: 'RFID') final String? rfid,
          @JsonKey(name: 'Name') final String? name,
          @JsonKey(name: 'Phone') final String? phone,
          @JsonKey(name: 'Phone2') final String? phone2,
          @JsonKey(name: 'Address') final String? address,
          @JsonKey(name: 'DriverNo') final String? driverNo,
          @JsonKey(name: 'Email') final String? email,
          @JsonKey(name: 'LicenseNo') final String? licenseNo,
          @JsonKey(name: 'RegisterPlace') final String? registerPlace,
          @JsonKey(name: 'Username') final String? username,
          @JsonKey(name: 'IsActivated') final bool isActivated,
          @JsonKey(name: 'RegisterDate') final int? registerDate,
          @JsonKey(name: 'ExpiredDate') final int? expiredDate,
          @JsonKey(name: 'Photo') final bool photo,
          @JsonKey(name: 'Fields') final Map<String, dynamic> fields,
          @JsonKey(name: 'VehicleId') final String? vehicleId}) =
      _$DriverDetailResponseImpl;
  const _DriverDetailResponse._() : super._();

  factory _DriverDetailResponse.fromJson(Map<String, dynamic> json) =
      _$DriverDetailResponseImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  String get id;
  @override
  @JsonKey(name: 'CityId')
  String? get cityId;
  @override
  @JsonKey(name: 'RFID')
  String? get rfid;
  @override
  @JsonKey(name: 'Name')
  String? get name;
  @override
  @JsonKey(name: 'Phone')
  String? get phone;
  @override
  @JsonKey(name: 'Phone2')
  String? get phone2;
  @override
  @JsonKey(name: 'Address')
  String? get address;
  @override
  @JsonKey(name: 'DriverNo')
  String? get driverNo;
  @override
  @JsonKey(name: 'Email')
  String? get email;
  @override
  @JsonKey(name: 'LicenseNo')
  String? get licenseNo;
  @override
  @JsonKey(name: 'RegisterPlace')
  String? get registerPlace;
  @override
  @JsonKey(name: 'Username')
  String? get username;
  @override
  @JsonKey(name: 'IsActivated')
  bool get isActivated;
  @override
  @JsonKey(name: 'RegisterDate')
  int? get registerDate;
  @override
  @JsonKey(name: 'ExpiredDate')
  int? get expiredDate;
  @override
  @JsonKey(name: 'Photo')
  bool get photo;
  @override
  @JsonKey(name: 'Fields')
  Map<String, dynamic> get fields;
  @override
  @JsonKey(name: 'VehicleId')
  String? get vehicleId;
  @override
  @JsonKey(ignore: true)
  _$$DriverDetailResponseImplCopyWith<_$DriverDetailResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
