// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_driver_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GetDriverRequestImpl _$$GetDriverRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$GetDriverRequestImpl(
      page: json['page'] as String,
      pageSize: json['pageSize'] as String,
      sort: json['sort'] as String?,
      search: json['search'] as String?,
    );

Map<String, dynamic> _$$GetDriverRequestImplToJson(
        _$GetDriverRequestImpl instance) =>
    <String, dynamic>{
      'page': instance.page,
      'pageSize': instance.pageSize,
      'sort': instance.sort,
      'search': instance.search,
    };
