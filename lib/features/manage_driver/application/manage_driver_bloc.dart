import 'dart:convert';

import 'package:avema_v2/application/base_bloc.dart';
import 'package:avema_v2/application/enums/avema_enums.dart';
import 'package:avema_v2/application/extensions/string_extension.dart';
import 'package:avema_v2/application/utils/avema_helper.dart';
import 'package:avema_v2/assets_manager/assets_manager.dart';
import 'package:avema_v2/features/manage_driver/data/city_response.dart';
import 'package:avema_v2/features/manage_driver/data/driver_detail_reponse.dart';
import 'package:avema_v2/features/manage_driver/data/driver_response.dart';
import 'package:avema_v2/features/manage_driver/data/get_driver_request.dart';
import 'package:avema_v2/features/manage_driver/data/plate_response.dart';
import 'package:avema_v2/features/manage_driver/repository/driver_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

part 'manage_driver_event.dart';
part 'manage_driver_state.dart';

class ManageDriverBloc extends BaseBloc<ManageDriverEvent, ManageDriverState> {
  final DriverRepository driverRepository;
  ManageDriverBloc(this.driverRepository) : super(ManageDriverState.initial()) {
    on<GetListDriverEvent>(_onGetListDriver);
    on<LoadMoreDriverEvent>(_onLoadMoreDriver);
    on<SearchDriverEvent>(_onSearchDriver);
    on<ClearSearchDriverEvent>(_onClearSearchDriver);
    on<UpdateActivateDriverEvent>(_onUpdateActivateDriver);
    on<SortTypeSelectedEvent>(_onSortTypeSelected);
    on<SortDriversByType>(_onSortDriversByType);
    on<PickImageEvent>(_onPickImage);
    on<ClearImageEvent>(_onClearImage);
    on<UpdateActivated>(_onUpdateActivated);
    on<InitActivated>(_onInitActivated);
    on<GetCityEvent>(_onGetCity);
    on<GetPlateEvent>(_onGetPlate);
    on<GetDriverDetailEvent>(_onGetDriverDetail);
    on<ClearDetailDriverEvent>(_onClearDetailDriver);
    on<ResetStateEvent>(_onResetState);
    on<DeleteDriverEvent>(_onDeleteDriver);
    on<GetDriverPhoto>(_onGetDriverPhoto);
  }

  Future<void> _onGetListDriver(
    GetListDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    if (event.isRefresh) {
      emit(state.copyWith(
        status: ManageDriverStatus.loading,
        currentPage: 1,
        hasMore: true,
      ));
    } else {
      emit(state.copyWith(status: ManageDriverStatus.loading));
    }

    emit(state.copyWith(status: ManageDriverStatus.loading));
    final response = await driverRepository.getAllDriver(
      request: event.request,
    );

    response.fold(
      (failure) {
        debugPrint('[ManageDriverBloc] _onGetListDriver $failure');
        emit(state.copyWith(status: ManageDriverStatus.failure));
      },
      (right) {
        const pageSize = 10;
        const startIndex = 0;
        const endIndex = pageSize;

        final result = right.data ?? [];
        final listDriver = result.sublist(
          startIndex,
          endIndex > result.length ? result.length : endIndex,
        );

        final hasMore = endIndex < result.length;

        emit(
          state.copyWith(
            status: ManageDriverStatus.success,
            listDriver: listDriver,
            listDriverSearched: listDriver,
            listDriverSorted: listDriver,
            currentPage: 1,
            hasMore: hasMore,
          ),
        );
      },
    );
  }

  Future<void> _onLoadMoreDriver(
    LoadMoreDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    // if (!state.hasMore || state.isLoadingMore) return;

    // emit(state.copyWith(isLoadingMore: true));

    // await Future.delayed(const Duration(seconds: 1));
    // String jsonString =
    //     await rootBundle.loadString(appAssets.assets.listDriverTest);
    // Map<String, dynamic> parsedJson = jsonDecode(jsonString);
    // final allDrivers = (parsedJson['data'] as List<dynamic>)
    //     .map((e) => DriverResponse.fromJson(e as Map<String, dynamic>))
    //     .toList();

    // // Simulate pagination: 10 items per page
    // const pageSize = 10;
    // final nextPage = state.currentPage + 1;
    // final startIndex = (nextPage - 1) * pageSize;
    // final endIndex = nextPage * pageSize;

    // if (startIndex >= allDrivers.length) {
    //   // No more data
    //   emit(state.copyWith(
    //     isLoadingMore: false,
    //     hasMore: false,
    //   ));
    //   return;
    // }

    // final newDrivers = allDrivers.sublist(
    //   startIndex,
    //   endIndex > allDrivers.length ? allDrivers.length : endIndex,
    // );

    // final hasMore = endIndex < allDrivers.length;

    // // Append new data to existing list
    // final updatedList = [...state.listDriver ?? [], ...newDrivers];

    // emit(
    //   state.copyWith(
    //     listDriver: updatedList,
    //     listDriverSearched: updatedList,
    //     listDriverSorted: updatedList,
    //     currentPage: nextPage,
    //     hasMore: hasMore,
    //     isLoadingMore: false,
    //   ),
    // );
  }

  Future<void> _onSearchDriver(
    SearchDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    final listSearched = state.listDriver
        ?.where((driver) => _searchingDriver(driver, event.searchKey.trim()))
        .toList();

    final listFiltered = state.sortTypeSelected != null
        ? _filterDriversByType(listSearched ?? [], state.sortTypeSelected!)
        : listSearched;

    final listSorted = state.sortTypeSelected != null
        ? _sortDrivers(listFiltered ?? [], state.sortTypeSelected!)
        : listFiltered;

    emit(
      state.copyWith(
        listDriverSearched: listSearched,
        listDriverSorted: listSorted,
      ),
    );
  }

  bool _searchingDriver(DriverData driver, String searchKey) {
    final fields = [
      driver.name,
      driver.city,
      driver.phone,
      driver.phone2,
      driver.rfid,
      driver.licenseNo,
    ];

    return fields.any((field) => field?.search(searchKey) ?? false);
  }

  Future<void> _onClearSearchDriver(
    ClearSearchDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    final listFiltered = state.sortTypeSelected != null
        ? _filterDriversByType(state.listDriver ?? [], state.sortTypeSelected!)
        : state.listDriver;

    final listSorted = state.sortTypeSelected != null
        ? _sortDrivers(listFiltered ?? [], state.sortTypeSelected!)
        : listFiltered;

    emit(
      state.copyWith(
        listDriverSearched: state.listDriver,
        listDriverSorted: listSorted,
      ),
    );
  }

  Future<void> _onUpdateActivateDriver(
    UpdateActivateDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    final listDriver = state.listDriver?.map((driver) {
      if (driver.id == event.id) {
        return driver.copyWith(
          isActivated: event.isActivated,
        );
      }
      return driver;
    }).toList();
    emit(state.copyWith(listDriver: listDriver));
  }

  Future<void> _onSortTypeSelected(
    SortTypeSelectedEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(sortTypeSelected: event.sortType));
  }

  Future<void> _onSortDriversByType(
    SortDriversByType event,
    Emitter<ManageDriverState> emit,
  ) async {
    final listFiltered = _filterDriversByType(event.listDriver, event.sortType);

    final listDriver = _sortDrivers(listFiltered, event.sortType);
    emit(state.copyWith(listDriverSorted: listDriver));
  }

  List<DriverData> _filterDriversByType(
    List<DriverData> listDriver,
    DriverSortType sortType,
  ) {
    if (sortType == DriverSortType.all) {
      return listDriver;
    }

    final fieldGetter = AvemaHelper.getFieldExtractor(sortType);
    return listDriver.where((driver) {
      final value = fieldGetter(driver);
      return value != null && value.isNotEmpty;
    }).toList();
  }

  List<DriverData> _sortDrivers(
    List<DriverData> listDriver,
    DriverSortType sortType,
  ) {
    if (sortType == DriverSortType.all) {
      return listDriver;
    }

    final listSort = List<DriverData>.from(listDriver);
    final fieldGetter = AvemaHelper.getFieldExtractor(sortType);

    listSort.sort((a, b) {
      final aValue = fieldGetter(a) ?? '';
      final bValue = fieldGetter(b) ?? '';
      return aValue.compareTo(bValue);
    });

    return listSort;
  }

  int getDriverCountByType(DriverSortType sortType) {
    if (state.listDriver == null) return 0;
    return _filterDriversByType(state.listDriver!, sortType).length;
  }

  Future<void> _onPickImage(
    PickImageEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(imagePicked: NullableValue(event.image)));
  }

  Future<void> _onClearImage(
    ClearImageEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(imagePicked: const NullableValue(null)));
  }

  Future<void> _onUpdateActivated(
    UpdateActivated event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(isActivated: event.isActivated));
  }

  Future<void> _onInitActivated(
    InitActivated event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(isActivated: event.isActivated));
  }

  Future<void> _onGetCity(
    GetCityEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    final response = await driverRepository.getCityList();
    response.fold(
      (failure) {
        emit(state.copyWith(listCity: null));
      },
      (result) {
        emit(state.copyWith(listCity: result));
      },
    );
  }

  Future<void> _onGetPlate(
    GetPlateEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    await Future.delayed(const Duration(seconds: 1));
    String jsonString =
        await rootBundle.loadString(appAssets.assets.listPlateTest);
    Map<String, dynamic> parsedJson = jsonDecode(jsonString);
    final listPlate = (parsedJson['data'] as List<dynamic>)
        .map((e) => PlateResponse.fromJson(e as Map<String, dynamic>))
        .toList();
    emit(state.copyWith(listPlate: listPlate));
  }

  Future<void> _onGetDriverDetail(
    GetDriverDetailEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(detailDriverStatus: DetailDriverStatus.loading));
    await Future.delayed(const Duration(seconds: 1));
    String jsonString =
        await rootBundle.loadString(appAssets.assets.detailDriver);
    Map<String, dynamic> parsedJson = jsonDecode(jsonString);
    final driverDetail = DriverDetailResponse.fromJson(parsedJson['data']);
    emit(
      state.copyWith(
        driverDetail: driverDetail,
        detailDriverStatus: DetailDriverStatus.success,
      ),
    );
  }

  Future<void> _onClearDetailDriver(
    ClearDetailDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(detailDriverStatus: DetailDriverStatus.initial));
  }

  Future<void> _onResetState(
    ResetStateEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(ManageDriverState.initial());
  }

  Future<void> _onDeleteDriver(
    DeleteDriverEvent event,
    Emitter<ManageDriverState> emit,
  ) async {
    emit(state.copyWith(deleteDriverStatus: DeleteDriverStatus.loading));
    await Future.delayed(const Duration(seconds: 1));
    emit(state.copyWith(deleteDriverStatus: DeleteDriverStatus.success));
  }

  Future<void> _onGetDriverPhoto(
    GetDriverPhoto event,
    Emitter<ManageDriverState> emit,
  ) async {
    // Check if already loading or already loaded
    if (state.loadingPhotos.contains(event.id) ||
        state.driverPhotos.containsKey(event.id)) {
      return;
    }

    // Mark as loading
    final newLoadingPhotos = Set<String>.from(state.loadingPhotos)
      ..add(event.id);
    emit(state.copyWith(loadingPhotos: newLoadingPhotos));

    final response = await driverRepository.getDriverPhoto(id: event.id);

    // Remove from loading set
    final updatedLoadingPhotos = Set<String>.from(state.loadingPhotos)
      ..remove(event.id);

    response.fold(
      (failure) {
        // Keep driverPhoto for backward compatibility
        debugPrint('Get driver photo failed: $failure');
        emit(state.copyWith(
          driverPhoto: null,
          loadingPhotos: updatedLoadingPhotos,
        ));
      },
      (photoUrl) {
        // Update the photos map with the new photo URL
        final newPhotos = Map<String, String>.from(state.driverPhotos)
          ..[event.id] = photoUrl;

        debugPrint('newPhotos: $newPhotos');

        // emit(state.copyWith(
        //   driverPhoto: photoUrl, // Keep for backward compatibility
        //   driverPhotos: newPhotos,
        //   loadingPhotos: updatedLoadingPhotos,
        // ));
      },
    );
  }
}
