part of 'manage_driver_bloc.dart';

class NullableValue<T> {
  final T? value;
  const NullableValue(this.value);
}

class ManageDriverState {
  final ManageDriverStatus status;
  final DetailDriverStatus detailDriverStatus;
  final DeleteDriverStatus deleteDriverStatus;
  final List<DriverData>? listDriver;
  final List<DriverData>? listDriverSearched;
  final bool? isActivated;
  final DriverSortType? sortTypeSelected;
  final List<DriverSortType> listSortType;
  final List<DriverData>? listDriverSorted;
  final XFile? imagePicked;
  final int currentPage;
  final bool hasMore;
  final bool isLoadingMore;
  final List<CityResponse>? listCity;
  final List<PlateResponse>? listPlate;
  final DriverDetailResponse? driverDetail;
  final String? driverPhoto;
  final Map<String, String> driverPhotos; // Map of driver ID to photo URL
  final Set<String> loadingPhotos; // Set of driver IDs currently loading photos

  const ManageDriverState({
    this.status = ManageDriverStatus.initial,
    this.detailDriverStatus = DetailDriverStatus.initial,
    this.deleteDriverStatus = DeleteDriverStatus.initial,
    this.listDriverSearched,
    this.listDriver,
    this.driverDetail,
    this.isActivated = false,
    this.sortTypeSelected = DriverSortType.all,
    this.listSortType = DriverSortType.values,
    this.listDriverSorted,
    this.imagePicked,
    this.currentPage = 1,
    this.hasMore = true,
    this.isLoadingMore = false,
    this.listCity,
    this.listPlate,
    this.driverPhoto,
    this.driverPhotos = const {},
    this.loadingPhotos = const {},
  });

  factory ManageDriverState.initial() {
    return const ManageDriverState();
  }

  ManageDriverState copyWith({
    ManageDriverStatus? status,
    DetailDriverStatus? detailDriverStatus,
    DeleteDriverStatus? deleteDriverStatus,
    List<DriverData>? listDriver,
    List<DriverData>? listDriverSearched,
    bool? isActivated,
    DriverSortType? sortTypeSelected,
    List<DriverSortType>? listSortType,
    List<DriverData>? listDriverSorted,
    NullableValue<XFile>? imagePicked,
    int? currentPage,
    bool? hasMore,
    bool? isLoadingMore,
    List<CityResponse>? listCity,
    List<PlateResponse>? listPlate,
    DriverDetailResponse? driverDetail,
    String? driverPhoto,
    Map<String, String>? driverPhotos,
    Set<String>? loadingPhotos,
  }) {
    return ManageDriverState(
      status: status ?? this.status,
      listDriver: listDriver ?? this.listDriver,
      listDriverSearched: listDriverSearched ?? this.listDriverSearched,
      driverDetail: driverDetail ?? this.driverDetail,
      isActivated: isActivated ?? this.isActivated,
      sortTypeSelected: sortTypeSelected ?? this.sortTypeSelected,
      listSortType: listSortType ?? this.listSortType,
      listDriverSorted: listDriverSorted ?? this.listDriverSorted,
      imagePicked: imagePicked != null ? imagePicked.value : this.imagePicked,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      listCity: listCity ?? this.listCity,
      listPlate: listPlate ?? this.listPlate,
      detailDriverStatus: detailDriverStatus ?? this.detailDriverStatus,
      deleteDriverStatus: deleteDriverStatus ?? this.deleteDriverStatus,
      driverPhoto: driverPhoto ?? this.driverPhoto,
      driverPhotos: driverPhotos ?? this.driverPhotos,
      loadingPhotos: loadingPhotos ?? this.loadingPhotos,
    );
  }
}
