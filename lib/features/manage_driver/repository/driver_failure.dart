import 'package:freezed_annotation/freezed_annotation.dart';

part 'driver_failure.freezed.dart';

@freezed
class DriverFailure with _$DriverFailure {
  const factory DriverFailure.unexpected({required String error}) =
      _DriverFailureUnexpected;

  const factory DriverFailure.unauthorized() =
      _DriverFailureUnauthorized;

  const factory DriverFailure.unauthenticated() =
      _DriverFailureUnauthenticated;

  const factory DriverFailure.serverError() = _DriverFailureServerError;

  const factory DriverFailure.noInternet() = _DriverFailureNoInternet;
}
