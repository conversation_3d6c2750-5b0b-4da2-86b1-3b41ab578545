import 'package:avema_v2/core/error_code.dart';
import 'package:avema_v2/features/manage_driver/data/city_response.dart';
import 'package:avema_v2/features/manage_driver/data/driver_detail_reponse.dart';
import 'package:avema_v2/features/manage_driver/data/driver_response.dart';
import 'package:avema_v2/features/manage_driver/data/get_driver_request.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:avema_v2/rest_client/main_rest_client.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import 'driver_failure.dart';

class DriverRepository implements IDriverRepository {
  @override
  Future<Either<DriverFailure, DriverResponse>> getAllDriver({
    required GetDriverRequest request,
  }) async {
    try {
      final response = await getIt<MainRESTClient>().getAllDriver(
        request: request,
      );

      return response.dataOption.fold(
        () {
          return response.errorOption.fold(
            () {
              return left(
                DriverFailure.unexpected(
                  error: '${BaseErrorCode.emptyResponse.valueCode} - DR-00',
                ),
              );
            },
            (e) {
              return left(
                DriverFailure.unexpected(
                  error: '${e.responseCode} - ${e.description} - DR-01',
                ),
              );
            },
          );
        },
        (result) => right(result),
      );
    } on DioException catch (e) {
      return left(
        DriverFailure.unexpected(
          error:
              '${e.response?.statusCode} - ${e.response?.statusMessage} - DR-02',
        ),
      );
    } catch (e) {
      return left(
        DriverFailure.unexpected(
          error: '$e - DR-03',
        ),
      );
    }
  }

  @override
  Future<Either<DriverFailure, List<CityResponse>>> getCityList() async {
    try {
      final response = await getIt<MainRESTClient>().getCityList();

      return response.dataOption.fold(
        () {
          return response.errorOption.fold(
            () {
              return left(
                DriverFailure.unexpected(
                  error: '${BaseErrorCode.emptyResponse.valueCode} - DR-04',
                ),
              );
            },
            (e) {
              return left(
                DriverFailure.unexpected(
                  error: '${e.responseCode} - ${e.description} - DR-05',
                ),
              );
            },
          );
        },
        (result) => right(result),
      );
    } on DioException catch (e) {
      return left(
        DriverFailure.unexpected(
          error:
              '${e.response?.statusCode} - ${e.response?.statusMessage} - DR-06',
        ),
      );
    } catch (e) {
      return left(
        DriverFailure.unexpected(
          error: '$e - DR-07',
        ),
      );
    }
  }

  @override
  Future<Either<DriverFailure, DriverDetailResponse>> getDriverDetail({
    required String id,
  }) async {
    try {
      final response = await getIt<MainRESTClient>().getDriverDetail(id: id);

      return response.dataOption.fold(
        () {
          return response.errorOption.fold(
            () {
              return left(
                DriverFailure.unexpected(
                  error: '${BaseErrorCode.emptyResponse.valueCode} - DR-08',
                ),
              );
            },
            (e) {
              return left(
                DriverFailure.unexpected(
                  error: '${e.responseCode} - ${e.description} - DR-09',
                ),
              );
            },
          );
        },
        (result) => right(result),
      );
    } on DioException catch (e) {
      return left(
        DriverFailure.unexpected(
          error:
              '${e.response?.statusCode} - ${e.response?.statusMessage} - DR-10',
        ),
      );
    } catch (e) {
      return left(
        DriverFailure.unexpected(
          error: '$e - DR-11',
        ),
      );
    }
  }

  @override
  Future<Either<DriverFailure, dynamic>> getDriverPhoto({
    required String id,
  }) async {
    try {
      final response = await getIt<MainRESTClient>().getDriverPhoto(id: id);

      return response.dataOption.fold(
        () {
          return response.errorOption.fold(
            () {
              return left(
                DriverFailure.unexpected(
                  error: '${BaseErrorCode.emptyResponse.valueCode} - DR-12',
                ),
              );
            },
            (e) {
              return left(
                DriverFailure.unexpected(
                  error: '${e.responseCode} - ${e.description} - DR-13',
                ),
              );
            },
          );
        },
        (result) => right(result),
      );
    } on DioException catch (e) {
      return left(
        DriverFailure.unexpected(
          error:
              '${e.response?.statusCode} - ${e.response?.statusMessage} - DR-14',
        ),
      );
    } catch (e) {
      return left(
        DriverFailure.unexpected(
          error: '$e - DR-15',
        ),
      );
    }
  }

  @override
  Future<Either<DriverFailure, String>> deleteDriver({
    required String id,
  }) async {
    try {
      final response = await getIt<MainRESTClient>().deleteDriver(id: id);

      return response.dataOption.fold(
        () {
          return response.errorOption.fold(
            () {
              return left(
                DriverFailure.unexpected(
                  error: '${BaseErrorCode.emptyResponse.valueCode} - DR-16',
                ),
              );
            },
            (e) {
              return left(
                DriverFailure.unexpected(
                  error: '${e.responseCode} - ${e.description} - DR-17',
                ),
              );
            },
          );
        },
        (result) => right(result),
      );  } on DioException catch (e) {
      return left(
        DriverFailure.unexpected(
          error:
              '${e.response?.statusCode} - ${e.response?.statusMessage} - DR-18',
        ),
      );
    } catch (e) {
      return left(
        DriverFailure.unexpected(
          error: '$e - DR-19',
        ),
      );
    }
  }
}

abstract class IDriverRepository {
  Future<Either<DriverFailure, DriverResponse>> getAllDriver({
    required GetDriverRequest request,
  });

  Future<Either<DriverFailure, List<CityResponse>>> getCityList();

  Future<Either<DriverFailure, DriverDetailResponse>> getDriverDetail({
    required String id,
  });

  Future<Either<DriverFailure, dynamic>> getDriverPhoto({
    required String id,
  });

  Future<Either<DriverFailure, String>> deleteDriver({
    required String id,
  });
}
