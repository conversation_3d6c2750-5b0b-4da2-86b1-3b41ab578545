import 'package:auto_size_text/auto_size_text.dart';
import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/assets_manager/assets_manager.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/manage_driver/presentations/manage_driver_screen.dart';
import 'package:avema_v2/features/manage_vehicle/presentations/manage_group_vehicle/all_group_screen.dart';
import 'package:avema_v2/features/manage_vehicle/presentations/manage_vehicle/list_vehicle_screen.dart';
import 'package:avema_v2/features/manage_vehicle/presentations/setting/setting_management_screen.dart';
import 'package:avema_v2/features/manage_vehicle/presentations/widgets/item_manage_view.dart';
import 'package:avema_v2/features/theme/avema_theme.dart';
import 'package:avema_v2/features/theme/theme_bloc.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:avema_v2/uis/animated_button.dart';
import 'package:flutter/material.dart';

class ManagementScreen extends StatefulWidget {
  const ManagementScreen({super.key});

  @override
  State<ManagementScreen> createState() => _ManagementScreenState();
}

class _ManagementScreenState extends State<ManagementScreen> {
  @override
  Widget build(BuildContext context) {
    final language = AVELanguages.instance;
    final theme = Theme.of(context).colorScheme;
    final isLightTheme = getIt<ThemeBloc>().state.isLightTheme();
    return Scaffold(
      appBar: AppBar(
          surfaceTintColor: Colors.transparent,
          backgroundColor: theme.primary,
          centerTitle: true,
          title: AutoSizeText(
            language.management,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w700,
            ),
          ),
          leading: AvemaButton(
            onTap: () => Navigator.pop(context),
            color: theme.primary,
            child: const Icon(
              Icons.arrow_back_ios_rounded,
              color: Colors.white,
            ),
          )),
      body: Container(
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height,
            minWidth: MediaQuery.of(context).size.width,
          ),
          padding: const EdgeInsets.only(
            left: 16,
            top: 16,
            right: 16,
          ),
          color: theme.surface,
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const ListVehicleScreen()));
                  },
                  child: Container(
                    height: 62,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: theme.surface,
                      borderRadius: const BorderRadius.all(Radius.circular(4)),
                      boxShadow: isLightTheme
                          ? AvemaTheme.lightBoxShadow
                          : AvemaTheme.darkBoxShadow,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.fire_truck,
                                color: theme.onSurface,
                              ),
                              const SizedBox(width: 16),
                              Text(
                                language.vehicle,
                                style: TextStyle(
                                  color: theme.onSurface,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                          Icon(
                            Icons.keyboard_arrow_right_outlined,
                            color: theme.onSurface,
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                GestureDetector(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const AllGroupScreen()));
                  },
                  child: Container(
                    height: 62,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: theme.surface,
                      borderRadius: const BorderRadius.all(Radius.circular(4)),
                      boxShadow: isLightTheme
                          ? AvemaTheme.lightBoxShadow
                          : AvemaTheme.darkBoxShadow,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.fire_truck,
                                color: theme.onSurface,
                              ),
                              const SizedBox(width: 16),
                              Text(
                                language.group,
                                style: TextStyle(
                                  color: theme.onSurface,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                          Icon(
                            Icons.keyboard_arrow_right_outlined,
                            color: theme.onSurface,
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                ItemManageView(
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SettingManagementScreen(),
                    ),
                  ),
                  title: language.settings,
                  icon: Icons.settings,
                ),
                context.verticalSpaceL,
                ItemManageView(
                  onTap: () => context.push(const ManageDriverScreen()),
                  title: language.driver,
                  pathIcon: assetsGen.assets.iconDriver.path,
                ),
              ],
            ),
          )),
    );
  }
}
