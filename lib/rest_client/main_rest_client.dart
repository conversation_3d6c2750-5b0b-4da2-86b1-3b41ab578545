import 'package:avema_v2/features/manage_driver/data/city_response.dart';
import 'package:avema_v2/features/manage_driver/data/driver_detail_reponse.dart';
import 'package:avema_v2/features/manage_driver/data/driver_response.dart';
import 'package:avema_v2/features/manage_driver/data/get_driver_request.dart';
import 'package:avema_v2/features/report/rfid/data/driver_rfid_response.dart';
import 'package:avema_v2/features/report/rfid/data/rfid_request.dart';
import 'package:avema_v2/features/report/rfid/data/rfid_respone.dart';
import 'package:avema_v2/features/report/tacho_graph/data/models/tacho_graph_request.dart';
import 'package:avema_v2/features/report/tacho_graph/data/models/tacho_graph_response.dart';
import 'package:avema_v2/features/report/tacho_graph/data/models/vehicle_response.dart';
import 'package:avema_v2/modal/base_response.dart';
import 'package:avema_v2/modal/device_setting/device_setting_request.dart';
import 'package:avema_v2/modal/device_setting/device_setting_response.dart';
import 'package:avema_v2/modal/device_setting/update_device_setting_request.dart';
import 'package:avema_v2/modal/error_response.dart';
import 'package:avema_v2/modal/fuel_pump/fuel_pump_request.dart';
import 'package:avema_v2/modal/live_stream/live_stream_request.dart';
import 'package:avema_v2/modal/live_stream/live_stream_response.dart';
import 'package:avema_v2/modal/live_stream/vehicle_setting_response.dart';
import 'package:avema_v2/modal/locator/base_locator_response.dart';
import 'package:avema_v2/modal/locator/detail_locator_response.dart';
import 'package:avema_v2/modal/locator/locator_create_request.dart';
import 'package:avema_v2/modal/locator/locator_get_all_request.dart';
import 'package:avema_v2/modal/locator/locator_get_all_response.dart';
import 'package:avema_v2/modal/locator/locator_params_response.dart';
import 'package:avema_v2/modal/locator/locator_update_request.dart';
import 'package:avema_v2/modal/locator/send_locator_link_to_email_request.dart';
import 'package:avema_v2/modal/login/login_request.dart';
import 'package:avema_v2/modal/login/login_response.dart';
import 'package:avema_v2/modal/login/logout_request.dart';
import 'package:avema_v2/modal/manage_vehicle/m_list_vehicle_group_request.dart';
import 'package:avema_v2/modal/manage_vehicle/m_list_vehicle_group_response.dart';
import 'package:avema_v2/modal/manage_vehicle/m_list_vehicle_request.dart';
import 'package:avema_v2/modal/manage_vehicle/m_list_vehicle_response.dart';
import 'package:avema_v2/modal/manage_vehicle/m_update_send_data_request.dart';
import 'package:avema_v2/modal/manage_vehicle/m_update_vehicle_group_request.dart';
import 'package:avema_v2/modal/manage_vehicle/m_update_vehicle_request.dart';
import 'package:avema_v2/modal/manage_vehicle/m_vehicle_detail_group_response.dart';
import 'package:avema_v2/modal/manage_vehicle/m_vehicle_detail_response.dart';
import 'package:avema_v2/modal/manage_vehicle/m_vehicle_setting_request.dart';
import 'package:avema_v2/modal/monitor_vehicle/monitor_vehicle_request.dart';
import 'package:avema_v2/modal/monitor_vehicle/monitor_vehicle_response.dart';
import 'package:avema_v2/modal/notification/notification_popup.dart';
import 'package:avema_v2/modal/notification/notification_popup_status_response.dart';
import 'package:avema_v2/modal/notification/notification_response.dart';
import 'package:avema_v2/modal/notification/notification_setting_request.dart';
import 'package:avema_v2/modal/notification/notification_setting_response.dart';
import 'package:avema_v2/modal/playback/playback_request.dart';
import 'package:avema_v2/modal/playback/playback_response.dart';
import 'package:avema_v2/modal/playback/timeline_request.dart';
import 'package:avema_v2/modal/playback/timeline_response.dart';
import 'package:avema_v2/modal/route_replay/history_route_request.dart';
import 'package:avema_v2/modal/route_replay/history_route_response.dart';
import 'package:avema_v2/modal/short_monitor_vehicle/vehicle_group_monitor_response.dart';
import 'package:avema_v2/modal/station/station_request.dart';
import 'package:avema_v2/modal/station/station_response.dart';
import 'package:avema_v2/modal/user/user_profile_request.dart';
import 'package:avema_v2/modal/vehicle_image/vehicle_image_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/http.dart';

part 'main_rest_client.g.dart';

/// AUTH \\\
/// LANGUAGE \\\
/// MONITOR \\\
/// HISTORY \\\
/// MDVR \\\
/// NOTIFICATION \\\
/// IMAGE \\\
/// LOCATOR \\\
/// FUEL PUMP \\\
/// VEHICLE \\\
/// VEHICLE GROUP \\\
/// DEVICE \\\

@RestApi(baseUrl: 'http://192.168.38.99:24567/api')
abstract class MainRESTClient {
  factory MainRESTClient(Dio dio, {String baseUrl}) = _MainRESTClient;

  /// AUTH \\\
  @POST('/v1/account/login')
  Future<BaseResponse<AvemaUser, ErrorResponse>> login({
    @Body() required LoginRequest request,
  });

  @POST('/v1/account/logout')
  Future<BaseResponse<String, ErrorResponse>> logout({
    @Body() required LogoutRequest request,
  });

  @POST('/v1/account/updateprofile')
  Future<BaseResponse<String, ErrorResponse>> updateProfile({
    @Body() required UserProfileRequest request,
  });

  /// LANGUAGE \\\
  @GET('/v1/language/getsupport')
  Future<BaseResponse<dynamic, ErrorResponse>> getLanguageSupport();

  @POST('/v1/language/get')
  Future<BaseResponse<dynamic, ErrorResponse>> getLanguage({
    @Body() required List<String> languagesSupported,
  });

  /// MONITOR \\\
  @POST('/v1/monitor/getvehiclemodelsbyjson')
  Future<BaseResponse<VehicleCompactResponse, ErrorResponse>> getVehicleGroup();

  @POST('/v1/monitor/refreshvehicle')
  Future<BaseResponse<MonitorVehicleResponse, ErrorResponse>>
      getMonitorVehicle({
    @Body() required MonitorVehicleRequest request,
  });

  @GET('/v1/mdvr/vehiclesettings')
  Future<BaseResponse<VehicleSettingResponse, ErrorResponse>>
      getVehicleSetting();

  @POST('/v1/map/stationgetlistbyjson')
  Future<BaseResponse<StationResponse, ErrorResponse>> getStation({
    @Body() required StationRequest request,
  });

  /// HISTORY \\\
  @POST('/v1/history/getwaypoints')
  Future<BaseResponse<HistoryRouteResponse, ErrorResponse>> getHistoryRoute({
    @Body() required HistoryRouteRequest request,
  });

  /// MDVR \\\
  @POST('/v1/mdvr/livestreams')
  Future<BaseResponse<List<LiveStreamData>, ErrorResponse>> getLiveStreams({
    @Body() required List<LiveStreamRequest> request,
  });

  @POST('/v1/mdvr/requestvideoplayback')
  Future<BaseResponse<PlaybackResponse, ErrorResponse>> getPlaybackVideo({
    @Body() required PlaybackRequest request,
  });

  @POST('/v1/mdvr/requesttimelinedata')
  Future<BaseResponse<TimelineResponse, ErrorResponse>> getListVideo({
    @Body() required TimelineRequest request,
  });

  /// NOTIFICATION \\\
  @GET('/v1/home/<USER>')
  Future<BaseResponse<NotificationResponse, ErrorResponse>>
      getListNotification();

  @GET('/v1/home/<USER>/{id}')
  Future<BaseResponse<String, ErrorResponse>> readNotification({
    @Path('id') required String id,
  });

  @GET('/v1/home/<USER>')
  Future<BaseResponse<String, ErrorResponse>> readAllNotification();

  @POST('/v1/home/<USER>')
  Future<BaseResponse<NotificationSettingResponse, ErrorResponse>>
      getNotificationSettings({
    @Body() required NotificationSettingRequest request,
  });

  @POST('/v1/home/<USER>')
  Future<BaseResponse<String, ErrorResponse>> updateNotificationSetting({
    @Query('id') required String id,
    @Query('active') required bool active,
  });

  @GET('/v1/home/<USER>')
  Future<BaseResponse<NotificationPopUpStatusResponse, ErrorResponse>>
      getNotificationPopUpStatus();

  @GET('/v1/home/<USER>')
  Future<BaseResponse<NotificationPopUp, ErrorResponse>> getNotificationPopUp({
    @Query('notificationId') required String id,
  });

  /// IMAGE \\\
  @GET('/v1/monitor/takevehicleimage/{id}')
  Future<BaseResponse<String, ErrorResponse>> takeVehicleImage({
    @Path('id') required String id,
  });

  @GET('/v1/monitor/viewcam')
  Future<BaseResponse<List<VehicleImage>, ErrorResponse>> loadAllImage({
    @Query('id') required String id,
    @Query('time') required int time,
  });

  /// LOCATOR \\\

  @POST('/v1/locator/getall')
  Future<BaseResponse<LocatorGetAllResponse, ErrorResponse>> getAllLocator({
    @Body() required LocatorGetAllRequest request,
  });

  @GET('/v1/locator/params')
  Future<BaseResponse<LocatorParamsResponse, ErrorResponse>> getLocatorParams();

  @POST('/v1/locator/create')
  Future<BaseResponse<BaseLocatorResponse, ErrorResponse>> createLocator({
    @Body() required LocatorCreateRequest request,
  });

  @POST('/v1/locator/update')
  Future<BaseResponse<String, ErrorResponse>> updateLocator({
    @Body() required LocatorUpdateRequest request,
  });

  @POST('/v1/locator/getbyid')
  Future<BaseResponse<DetailLocatorResponse, ErrorResponse>> getLocatorById({
    @Query('id') required String id,
  });

  @POST('/v1/locator/delete')
  Future<BaseResponse<String, ErrorResponse>> deleteLocator({
    @Query('id') required String id,
  });

  @POST('/v1/locator/sendlocatorlinktoemail')
  Future<BaseResponse<String, ErrorResponse>> sendLocatorToEmail({
    @Body() required SendLocatorLinkToMailRequest request,
  });

  @POST('/v1/locator/getlocatorlink')
  Future<BaseResponse<BaseLocatorResponse, ErrorResponse>> getLocatorLink({
    @Query('id') required String id,
    @Query('userId') required String userId,
  });

  /// FUEL PUMP \\\
  @POST('/v1/monitor/sendcommand')
  Future<BaseResponse<String, ErrorResponse>> sendCommand({
    @Body() required FuelPumpRequest fuelPumpRequest,
  });

  /// VEHICLE \\\
  @POST('/v1/vehicle/getall')
  Future<BaseResponse<MListVehicleModalResponse, ErrorResponse>>
      mGetAllVehicle({
    @Body() required MListVehicleModalRequest request,
  });

  @GET('/v1/vehicle/getbyid/{id}')
  Future<BaseResponse<MVehicleDetailResponse, ErrorResponse>>
      mGetVehicleDetail({
    @Path('id') required String id,
  });

  @POST('/v1/vehicle/update')
  Future<BaseResponse<String, ErrorResponse>> mUpdateVehicle({
    @Body() required MUpdateVehicleRequest request,
  });

  @POST('/v1/vehicle/getvehiclebussettinglistbyjson')
  Future<BaseResponse<String, ErrorResponse>> getVehicleBusSettingList({
    @Body() required MVehicleSettingRequest request,
  });

  @POST('/v1/vehicle/updatesenddata')
  Future<BaseResponse<String, ErrorResponse>> updateSendData({
    @Body() required MUpdateSendDataRequest request,
  });

  @POST('/v1/vehicle/getvehiclespecialcargosettinglistbyjson')
  Future<BaseResponse<String, ErrorResponse>> getVehicleSpecialCarGoSetting({
    @Body() required MVehicleSettingRequest request,
  });

  @POST('/v1/vehicle/getvehiclelongtermcaresettinglistbyjson')
  Future<BaseResponse<String, ErrorResponse>> getVehicleLongTermCareSetting({
    @Body() required MVehicleSettingRequest request,
  });

  /// VEHICLE GROUP \\\
  @POST('/v1/vehiclegroup/getall')
  Future<BaseResponse<MListVehicleGroupResponse, ErrorResponse>>
      mGetAllVehicleGroup({
    @Body() required MListVehicleGroupRequest request,
  });

  @GET('/v1/vehiclegroup/getbyid/{id}')
  Future<BaseResponse<MVehicleDetailGroupResponse, ErrorResponse>>
      mGetDetailVehicleGroup({
    @Path('id') required String id,
  });

  @POST('/v1/vehiclegroup/create')
  Future<BaseResponse<String, ErrorResponse>> mCreateVehicleGroup({
    @Body() required String plate,
  });

  @POST('/v1/vehiclegroup/update')
  Future<BaseResponse<String, ErrorResponse>> mUpdateVehicleGroup({
    @Body() required MUpdateVehicleGroupRequest request,
  });

  @POST('/v1/vehiclegroup/delete/{id}')
  Future<BaseResponse<String, ErrorResponse>> mDeleteVehicleGroup({
    @Path('id') required String id,
  });

  /// DEVICE \\\
  @POST('/v1/vehicle/getdevicesettinginlist')
  Future<BaseResponse<ListDeviceSettingResponse, ErrorResponse>>
      getListDeviceSetting({
    @Body() required ListDeviceSettingRequest request,
  });

  @POST('/v1/vehicle/updatedevicesetting')
  Future<BaseResponse<String, ErrorResponse>> updateDeviceSetting({
    @Body() required UpdateDeviceSettingRequest request,
  });

  /// FIREBASE \\\\

  // @GET('/v1/language/getlanguagesbyjson')
  // Future<BaseResponse<>>

  /// REPORT \\\
  @POST('/v1/tachographregulation/gettachographregulation')
  Future<BaseResponse<TachoGraphResponse, ErrorResponse>> getTachoGraph({
    @Body() required TachoGraphRequest request,
  });

  @GET('/v1/tachographregulation/getvehiclemodelsbyjson')
  Future<BaseResponse<List<VehicleResponse>, ErrorResponse>> getVehicles();

  @GET('/v1/report/get-driver-model-by-json')
  Future<BaseResponse<List<DriverRifdResponse>, ErrorResponse>> getDrivers();

  @POST('/v1/report/vehicle-rfid-report')
  Future<BaseResponse<RfidResponse, ErrorResponse>> getRfidReport({
    @Body() required RfidRequest request,
  });

  //DRIVER
  @POST('/v1/driver/getlistbyjson')
  Future<BaseResponse<DriverResponse, ErrorResponse>> getAllDriver({
    @Body() required GetDriverRequest request,
  });

  @POST('/v1/driver/getcitylistbyjson')
  Future<BaseResponse<List<CityResponse>, ErrorResponse>> getCityList();

  @POST('/v1/driver/get')
  Future<BaseResponse<DriverDetailResponse, ErrorResponse>> getDriverDetail({
    @Body() required String id,
  });

  @GET('/v1/driver/getphoto/{id}')
  Future<BaseResponse<dynamic, ErrorResponse>> getDriverPhoto({
    @Path('id') required String id,
  });

  @DELETE('/v1/driver/delete/{id}')
  Future<BaseResponse<String, ErrorResponse>> deleteDriver({
    @Path('id') required String id,
  });
}