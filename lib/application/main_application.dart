import 'package:avema_v2/application/global_bloc_listener.dart';
import 'package:avema_v2/application/main_channel_listener.dart';
import 'package:avema_v2/features/firebase/avema_firebase.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/notification_in_app/applications/receiver_notification_bloc.dart';
import 'package:avema_v2/features/splash/splash_screen.dart';
import 'package:avema_v2/features/theme/theme_bloc.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:overlay_support/overlay_support.dart';

/// FIREBSAE - NOTIFICATION \\\
/// DIO \\\
/// CACHE \\\

class MainApplication extends StatefulWidget {
  const MainApplication({super.key});

  @override
  State<MainApplication> createState() => _MainApplicationState();
}

class _MainApplicationState extends State<MainApplication>
    with WidgetsBindingObserver {
  final GlobalListenerManager _globalListener = GlobalListenerManager();

  final FirebaseAnalyticsObserver _firebaseAnalyticsObserver =
      FirebaseAnalyticsObserver(analytics: FirebaseAnalytics.instance);

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    _globalListener.registerGlobalListener();
    _globalListener.locationListener();
  }

  @override
  void dispose() {
    _globalListener.disposeAllSub();
    _disposeNotificationInApp();
    _diseposeListenerFirebaseMessage();
    AVELanguages.instance.cancelFirebaseTimer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ThemeBloc, ThemeState>(
      listener: (context, themeState) {
        ///
      },
      builder: (context, themeState) {
        final currentTheme = themeState.getThemeMode();
        final theme = themeState.getThemeWithCustomPrimary();

        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            boldText: false,
            textScaler: const TextScaler.linear(1.0),
          ),
          child: OverlaySupport.global(
            child: GlobalBlocListener(
              child: MaterialApp(
                navigatorObservers: kDebugMode
                    ? []
                    : <NavigatorObserver>[_firebaseAnalyticsObserver],
                localizationsDelegates: context.localizationDelegates,
                supportedLocales: context.supportedLocales,
                locale: context.locale,
                themeMode: currentTheme,
                theme: theme,
                darkTheme: theme,
                home: const SplashScreen(),
              ),
            ),
          ),
        );
      },
    );
  }

  /// FIREBSAE - NOTIFICATION \\\

  Future<void> _diseposeListenerFirebaseMessage() async {
    await AvemaFirebase.instance.dispose();
  }

  Future<void> _disposeNotificationInApp() async {
    getIt<ReceiverNotificationBloc>()
        .add(const ReceiverNotificationEvent.dispose());
  }
}
