enum ManageDriverStatus {
  initial,
  loading,
  success,
  failure,
}

enum DriverSortType {
  all("all"),
  city('city'),
  rfid('rfid'),
  driver<PERSON>o('driverNo'),
  driver('driver'),
  licenseNo('licenseNo'),
  phone('phone'),
  phone2('phone2');

  const DriverSortType(this.value);
  final String value;

  static DriverSortType? fromCode(String? code) {
    if (code == null) return null;
    try {
      return DriverSortType.values.firstWhere((e) => e.value == code);
    } catch (e) {
      return null;
    }
  }
}

enum DetailDriverStatus {
  initial,
  loading,
  success,
  failure,
}

enum DeleteDriverStatus {
  initial,
  loading,
  success,
  failure,
}

enum GetRegulationStatus {
  initial,
  loading,
  success,
  failure,
}

enum ChartViewType {
  regulation,
  tradition,
}

enum ChartType {
  line,
  polar,
}

enum GetRfidStatus {
  initial,
  loading,
  success,
  failure,
}

enum StatusGetTachoGraph {
  initial(0),
  loading(1),
  success(2);

  const StatusGetTachoGraph(this.value);
  final int value;
}

enum GetVehicleStatus {
  initial,
  loading,
  success,
  failure,
}

enum GetDriverStatus {
  initial,
  loading,
  success,
  failure,
}
