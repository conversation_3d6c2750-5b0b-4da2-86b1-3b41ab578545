import 'dart:convert';

import 'package:avema_v2/application/utils/avema_helper.dart';
import 'package:avema_v2/modal/login/login_response.dart';
import 'package:avema_v2/modal/login/user_password_modal.dart';
import 'package:avema_v2/modal/setting/setting_modal.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

@lazySingleton
class LocalCache {
  ///
  /// ------
  ///
  static const _AVEMA_USER = "avema_user";
  static const _FIREBASE_TOKEN = "firebase_token";
  static const _MONITOR_SETTING = 'monitor_setting';
  static const _HISTORY_SETTING = 'history_setting';
  static const _USERPASS = 'user_pass';
  static const _LIST_FILTER_VEHICLE_ID = 'list_filter_vehicle_id';
  static const _LANGUAGE = 'language';
  static const _DEVICE_ID = 'device_id';

  ///
  /// ---SAVE---
  ///
  Future<void> saveAvemaUser({
    required AvemaUser avemaUser,
  }) async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.setString(_AVEMA_USER, jsonEncode(avemaUser.toJson()));
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCS-E001',
      );
    }
  }

  Future<void> saveFirebaseToken({
    required String firebaseToken,
  }) async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.setString(_FIREBASE_TOKEN, firebaseToken);
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCS-E002',
      );
    }
  }

  Future<void> saveMonitorSetting({
    required MonitorSettingModal setting,
  }) async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.setString(_MONITOR_SETTING, jsonEncode(setting.toJson()));
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCS-E003',
      );
    }
  }

  Future<void> saveHistorySetting({
    required HistorySettingModal setting,
  }) async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.setString(_HISTORY_SETTING, jsonEncode(setting.toJson()));
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCS-E004',
      );
    }
  }

  Future<void> saveUsernameAndPassword(
      {required UsernameAndPassword userPassword}) async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.setString(_USERPASS, jsonEncode(userPassword.toJson()));
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCS-E005',
      );
    }
  }

  Future<void> saveFilterVehicleIds({
    required List<String> ids,
  }) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_LIST_FILTER_VEHICLE_ID, ids);
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCS-E006',
      );
    }
  }

  Future<void> saveLanguage({
    required String language,
  }) async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.setString(_LANGUAGE, language);
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCS-E007',
      );
    }
  }

  Future<void> saveDeviceId({
    required String deviceId,
  }) async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      await pref.setString(_DEVICE_ID, deviceId);
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCS-E008',
      );
    }
  }

  ///
  /// ---GET---
  ///
  Future<AvemaUser?> getAvemaUser() async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      final String? jsonString = pref.getString(_AVEMA_USER);
      if (jsonString != null) {
        final Map<String, dynamic> json = jsonDecode(jsonString);
        return AvemaUser.fromJson(json);
      } else {
        return null;
      }
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCG-E001',
      );

      return null;
    }
  }

  Future<String?> getFirebaseToken() async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      final String? token = pref.getString(_FIREBASE_TOKEN);
      return token;
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCG-E002',
      );

      return null;
    }
  }

  Future<MonitorSettingModal?> getMonitorSettingModal() async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      final String? jsonString = pref.getString(_MONITOR_SETTING);
      if (jsonString != null) {
        final Map<String, dynamic> json = jsonDecode(jsonString);
        return MonitorSettingModal.fromJson(json);
      } else {
        return null;
      }
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCG-E003',
      );

      return null;
    }
  }

  Future<HistorySettingModal?> getHistorySettingModal() async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      final String? jsonString = pref.getString(_HISTORY_SETTING);
      if (jsonString != null) {
        final Map<String, dynamic> json = jsonDecode(jsonString);
        return HistorySettingModal.fromJson(json);
      } else {
        return null;
      }
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCG-E004',
      );

      return null;
    }
  }

  Future<UsernameAndPassword?> getUsernameAndPassword() async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      final String? jsonString = pref.getString(_USERPASS);
      if (jsonString != null) {
        final Map<String, dynamic> json = jsonDecode(jsonString);
        return UsernameAndPassword.fromJson(json);
      } else {
        return null;
      }
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCG-E005',
      );

      return null;
    }
  }

  Future<List<String>?> getListFilterVehicleId() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final List<String>? stringList =
          prefs.getStringList(_LIST_FILTER_VEHICLE_ID);
      return stringList;
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCG-E006',
      );
      return null;
    }
  }

  Future<String?> getLanguage() async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      final String? language = pref.getString(_LANGUAGE);
      return language;
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCG-E007',
      );
      return null;
    }
  }

  Future<String?> getDeviceId() async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      final String? deviceId = pref.getString(_DEVICE_ID);
      return deviceId;
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LCG-E008',
      );
      return null;
    }
  }

  ///
  /// ---CLEAN---
  ///
  Future clean() async {
    try {
      final SharedPreferences pref = await SharedPreferences.getInstance();
      pref.remove(_AVEMA_USER);
      pref.remove(_FIREBASE_TOKEN);
      pref.remove(_MONITOR_SETTING);
      pref.remove(_HISTORY_SETTING);
      pref.remove(_LIST_FILTER_VEHICLE_ID);
      pref.remove(_LANGUAGE);
    } catch (e) {
      AvemaHelper.logError(
        error: e.toString(),
        title: 'LOCAL-CACHE',
        errorCode: 'LC-E001',
      );
    }
  }
}
