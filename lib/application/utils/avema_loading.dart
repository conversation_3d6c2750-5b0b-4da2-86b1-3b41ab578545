import 'package:avema_v2/assets_manager/assets_manager.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class AvemaLoading extends StatelessWidget {
  final double size;
  const AvemaLoading({super.key, this.size = 175});

  @override
  Widget build(BuildContext context) {
    return Lottie.asset(
      assetsGen.assets.animationCarLoading,
      height: size,
      width: size,
    );
  }
}
