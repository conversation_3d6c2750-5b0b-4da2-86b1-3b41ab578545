import 'package:avema_v2/application/extensions/context_extension.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/theme/avema_text_style.dart';
import 'package:avema_v2/features/theme/avema_theme.dart';
import 'package:avema_v2/uis/animated_button.dart';
import 'package:flutter/material.dart';

class AvemaDialogUtil {
  static Future<void> show(
    BuildContext context, {
    String? title,
    String? content,
    Widget? widgetContent,
    Function()? onYes,
    Function()? onCancel,
  }) async {
    final theme = Theme.of(context).colorScheme;
    final language = AVELanguages.instance;
    return showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          backgroundColor: theme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            title ?? language.confirm,
            style: AvemaTextStyle.titleMedium.copyWith(
              color: theme.onSurface,
            ),
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: Avema<PERSON>utton(
                    color: theme.surface,
                    onTap: () {
                      Navigator.pop(context);
                      onCancel?.call();
                    },
                    child: Text(
                      language.cancel,
                      textAlign: TextAlign.center,
                      style: AvemaTextStyle.bodyMedium.copyWith(
                        color: theme.onSurface,
                      ),
                    ),
                  ),
                ),
                context.horizontalSpaceM,
                Expanded(
                  child: AvemaButton(
                    color: theme.primary,
                    onTap: () {
                      Navigator.pop(context);
                      onYes?.call();
                    },
                    child: Text(
                      language.oke,
                      textAlign: TextAlign.center,
                      style: AvemaTextStyle.bodyMedium.copyWith(
                        color: AvemaColor.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
          content: widgetContent ??
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    content ?? '',
                    style: AvemaTextStyle.bodyMedium.copyWith(
                      color: theme.onSurface,
                    ),
                  ),
                  context.verticalSpaceM,
                ],
              ),
        );
      },
    );
  }
}
