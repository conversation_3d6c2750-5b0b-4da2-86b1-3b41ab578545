import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:avema_v2/application/enums/avema_enums.dart';
import 'package:avema_v2/application/utils/date_time_extension.dart';
import 'package:avema_v2/application/utils/local_cache.dart';
import 'package:avema_v2/core/api_response_status.dart';
import 'package:avema_v2/features/about_us/about_us_screen.dart';
import 'package:avema_v2/features/manage_driver/data/driver_response.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:avema_v2/modal/monitor_vehicle/monitor_vehicle_response.dart';
import 'package:avema_v2/uis/latlng_service_option.dart';
import 'package:dartz/dartz.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:latlong2/latlong.dart' as latlng2;
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';

class AvemaHelper {
  static final AvemaHelper _instance = AvemaHelper._internal();

  AvemaHelper._internal();

  static AvemaHelper get instance => _instance;

  static const String defaultVehicleImate =
      'lib/assets/vehicle_0_green_100.png';

  static const String culture = 'culture';
  static const String os = 'os';
  static const String appVersion = 'appVersion';
  static const String deviceId = 'deviceId';
  static const String deviceModel = 'deviceModel';
  static const String accept = 'accept';
  static const String acceptValue = '*/*';
  static const String contentType = 'Content-Type';
  static const String contentTypeValue = 'application/json';

  static Future<void> openLatLngService({
    required latlng2.LatLng latlng,
    required BuildContext context,
  }) async {
    try {
      await showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return LatlngServiceOptions(
            latlng: latlng,
          );
        },
      );
    } catch (e) {
      ///
    }
  }

  static Future<void> openGoogleMaps(latlng2.LatLng latlng) async {
    try {
      final Uri url = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=${latlng.latitude},${latlng.longitude}',
      );

      if (await canLaunchUrl(url)) {
        await launchUrl(url);
      } else {
        ///
      }
    } catch (e) {
      ///
    }
  }

  static Future<String> getDeviceModel() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        final String androidModel = androidInfo.model.replaceAll(" ", "");

        return androidModel;
      }
      if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        final String iosModel = iosInfo.name.replaceAll(" ", "");

        return iosModel;
      }

      return '';
    } catch (e) {
      return '';
    }
  }

  static Future<bool> getIphoneModel() async {
    if (Platform.isIOS) {
      final deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      final String model = iosInfo.utsname.machine;

      const iPhone11Models = [
        'iPhone12,1', // iPhone 11
        'iPhone12,3', // iPhone 11 Pro
        'iPhone12,5', // iPhone 11 Pro Max
      ];

      return iPhone11Models.contains(model);
    }
    return false;
  }

  static Future<String> getDeviceOS() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        final String androidOS =
            'Android${androidInfo.version.sdkInt.toString()}'
                .replaceAll(" ", "");

        return androidOS;
      }
      if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        final String iosInformation =
            '${iosInfo.systemName}${iosInfo.systemVersion}'.replaceAll(" ", "");

        return iosInformation;
      }

      return '';
    } catch (e) {
      return '';
    }
  }

  static Future<String> getVersionFromManifest() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      return '';
    }
  }

  static Future<String> getDeviceId() async {
    try {
      final localCache = getIt<LocalCache>();
      final deviceIdFromCache = await localCache.getDeviceId();

      if (deviceIdFromCache != null && deviceIdFromCache.length < 10) {
        return deviceIdFromCache;
      } else {
        const Uuid uuid = Uuid();
        final String generatedUuid = uuid.v4().substring(0, 8);
        await localCache.saveDeviceId(deviceId: generatedUuid);
        return generatedUuid;
      }
    } catch (e) {
      return '';
    }
  }

  static void copyText(String text, BuildContext context) {
    Clipboard.setData(ClipboardData(text: text));

    final snackBar = SnackBar(
      content: Container(
        padding: EdgeInsets.zero,
        constraints: BoxConstraints(
          maxHeight: 100,
          minHeight: 75,
          minWidth: MediaQuery.of(context).size.width,
          maxWidth: MediaQuery.of(context).size.width,
        ),
        child: Text(
          'Copied \n "$text"',
        ),
      ),
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 5),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  static Future<bool> accessCheckingAPIScreen(BuildContext context) async {
    final result = await showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => const EnterSecretPassView());

    if (result is bool) {
      return result;
    }

    return false;
  }

  static int getVersionApp({required String versionAppByString}) {
    List<String> parts = versionAppByString.split('.');
    int multiplier = 10000;

    int versionInt = 0;
    for (String part in parts) {
      int partInt = int.parse(part);
      versionInt += partInt * multiplier;
      multiplier ~/= 100;
    }

    return versionInt;
  }

  static void logError({
    required String error,
    required String title,
    required String errorCode,
  }) {
    log('''
          '===$title==='
            // $errorCode: $error
          ==================
          ''');
  }

  static String getLocatorLinkShare({
    required String id,
    required String token,
  }) {
    return "https://avema-iot.com/locator?id=$id&token=$token";
  }

  static String getLocatorString({
    required String id,
    required String token,
    required Vehicle? vehicle,
    required bool isEnglish,
    bool? haveQRCode,
  }) {
    return isEnglish
        ? '''
${vehicle != null ? 'We are sharing the plate number: ${vehicle.plate} current status for you' : ''}
More information please visit: https://avema-iot.com/locator?id=$id&token=$token
'''
        : '''
${vehicle != null ? '我正在分享車牌為${vehicle.plate}的車輛狀態！' : ''}
詳細信息請訪問: https://avema-iot.com/locator?id=$id&token=$token
''';
  }

  static Future<Either<L, R>> handleDioException<L, R>({
    required DioException error,
    required L Function() emptyResponseFailure,
    required L unauthenticatedFailure,
    required L unauthorizedFailure,
    required L serverErrorFailure,
    required L noInternetFailure,
    required L Function(int, String) unexpectedFailure,
  }) async {
    final bool hasConnection = await InternetConnectionChecker().hasConnection;
    if (!hasConnection) {
      return left(noInternetFailure);
    }

    return optionOf(error.response).fold(() {
      return left(emptyResponseFailure());
    }, (a) {
      switch (a.statusCode) {
        case APIResponseStatus.unauthenticated:
          // 401
          return left(unauthenticatedFailure);

        case APIResponseStatus.unauthorized:
          // 403
          return left(unauthorizedFailure);

        case APIResponseStatus.internalServerError:
          // 500
          return left(serverErrorFailure);

        default:
          // lỗi ngoại lệ
          return left(unexpectedFailure(
            a.statusCode ?? 000,
            a.statusMessage ?? 'ERROR',
          ));
      }
    });
  }

  static String datePickerFormat = "yyyy MM dd HH:mm";

  static Timer debouncer({
    required Timer? debounce,
    required Function() callExcution,
    Duration? duration,
  }) {
    debounce?.cancel();
    return Timer(
      duration ?? const Duration(milliseconds: 500),
      callExcution,
    );
  }

  static void setDioAuthHeaders({
    required String? accessToken,
    required String? username,
    String? typeToken = 'Bearer',
  }) {
    getIt<Dio>().options.headers['Authorization'] = '$typeToken $accessToken';
    getIt<Dio>().options.headers['user'] = username;
  }

  static void Function()? onUnAuthenticated;

  static bool isHls(String url) {
    return url.contains(".m3u8");
  }

  static Future<void> selectDate(
    BuildContext context, {
    required TextEditingController controller,
  }) async {
    final initDate = controller.text.isEmpty
        ? DateTime.now()
        : DateTime.parse(controller.text);

    final date = await showDatePicker(
      context: context,
      initialDate: initDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );

    if (date != null) {
      controller.text = date.getDateWithFormat(
        format: DateFormatType.yyyy_mm_dd,
      );
    }
  }

  static Future<void> selectTime(
    BuildContext context, {
    TimeOfDay? initialTime,
    Function(TimeOfDay?)? onSelected,
  }) async {
    final time = await showTimePicker(
      context: context,
      initialTime: initialTime ?? TimeOfDay.now(),
      builder: (context, child) => MediaQuery(
        data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
        child: child!,
      ),
    );

    if (time != null) {
      onSelected?.call(time);
    }
  }

  static String? Function(DriverData) getFieldExtractor(
    DriverSortType sortType,
  ) {
    switch (sortType) {
      case DriverSortType.city:
        return (driver) => driver.city;
      case DriverSortType.rfid:
        return (driver) => driver.rfid;
      case DriverSortType.driverNo:
        return (driver) => driver.driverNo;
      case DriverSortType.driver:
        return (driver) => driver.name;
      case DriverSortType.licenseNo:
        return (driver) => driver.licenseNo;
      case DriverSortType.phone:
        return (driver) => driver.phone;
      case DriverSortType.phone2:
        return (driver) => driver.phone2;
      case DriverSortType.all:
        return (driver) => null;
    }
  }
}
