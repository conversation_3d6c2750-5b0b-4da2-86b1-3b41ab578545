import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;

extension AvemaDateTime on DateTime {
  String taiwanFormat() {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(this);
  }

  String getDateWithFormat({required DateFormatType format}) {
    switch (format) {
      case DateFormatType.mmdd_hhmm:
        return DateFormat('MM-dd HH:mm').format(this);

      case DateFormatType.mm_dd_yy:
        return DateFormat('MM/dd/yy').format(this);

      case DateFormatType.mm_dd_yyyy:
        return DateFormat('MM/dd/yyyy').format(this);
      case DateFormatType.hhmmss:
        return DateFormat('HH:mm:ss').format(this);

      case DateFormatType.yyyy_mm_dd:
        return DateFormat('yyyy-MM-dd').format(this);

      case DateFormatType.hhmm:
        return DateFormat('hh:mm').format(this);

      case DateFormatType.HHmm:
        return DateFormat('HH:mm').format(this);  
      
      case DateFormatType.taiwan_format:
        return DateFormat('yyyy-MM-dd').format(this);

      case DateFormatType.yyyy_MM_dd_hh_mm_ss:
        return DateFormat('yyyy-MM-dd HH:mm:ss').format(this);

      default:
    }
    return DateFormat('MM-dd HH:mm').format(this);
  }

  int convertDateTimeToTimeFrom2010() {
    DateTime date = DateTime(2010);
    return ((millisecondsSinceEpoch - date.millisecondsSinceEpoch) / 1000)
        .round();
  }

  ///[timeAgo]
  String timeAgo({String? onNow}) {
    final now = DateTime.now();
    if ((millisecondsSinceEpoch ~/ 1000 - now.millisecondsSinceEpoch ~/ 1000)
            .abs() <
        60) {
      return onNow ?? 'Now';
    }

    timeago.setLocaleMessages('zh', timeago.ZhMessages());
    return timeago.format(
      this,
      locale: 'zh',
    );
  }
}

enum DateFormatType {
  mmdd_hhmm,
  mm_dd_yyyy,
  mm_dd_yy,
  hhmmss,
  yyyy_mm_dd,
  hhmm,
  HHmm,
  taiwan_format,
  yyyy_MM_dd_hh_mm_ss,
}

extension AvemaParseTimeEx on int {
  String getStringDateByFormatFromInt2010() {
    DateTime date = DateTime(2010);

    return date.add(Duration(seconds: this)).taiwanFormat();
  }

  String getStringDateByFormatFromInt2010Format(
      {required DateFormatType format}) {
    DateTime date = DateTime(2010);

    return date.add(Duration(seconds: this)).getDateWithFormat(format: format);
  }

  DateTime getDateByFormatFromInt2010() {
    DateTime date = DateTime(2010);

    return date.add(Duration(seconds: this));
  }

  String getFormatStringIntTime({bool hideSecond = false}) {
    final duration = Duration(seconds: this);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final remainingSeconds = duration.inSeconds % 60;

    return '${hours > 0 ? '${hours}h ' : ''}${minutes > 0 ? '${minutes}m ' : ''}${hideSecond ? '' : '${remainingSeconds}s'}';
  }

  String getFormatStringIntTimeWithoutSecond() {
    final duration = Duration(seconds: this);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    return '${hours > 0 ? '${hours}h ' : ''}${minutes > 0 ? '${minutes}m ' : ''}';
  }

  String formatFromMilliseconds() {
    return DateTime.fromMillisecondsSinceEpoch(this).getDateWithFormat(
      format: DateFormatType.yyyy_mm_dd,
    );
  }
}

enum Weekday {
  monday,
  tuesday,
  wednesday,
  thursday,
  friday,
  saturday,
  sunday,
}

extension WeekdayEx on Weekday {
  String getShortString() {
    final language = AVELanguages.instance;

    switch (this) {
      case Weekday.monday:
        return language.mondayShort;
      case Weekday.tuesday:
        return language.tuesdayShort;
      case Weekday.wednesday:
        return language.wednesdayShort;
      case Weekday.thursday:
        return language.thursdayShort;
      case Weekday.friday:
        return language.fridayShort;
      case Weekday.saturday:
        return language.saturdayShort;
      case Weekday.sunday:
        return language.sundayShort;
      default:
        return '';
    }
  }

  String getString() {
    final language = AVELanguages.instance;

    switch (this) {
      case Weekday.monday:
        return language.monday;
      case Weekday.tuesday:
        return language.tuesday;
      case Weekday.wednesday:
        return language.wednesday;
      case Weekday.thursday:
        return language.thursday;
      case Weekday.friday:
        return language.friday;
      case Weekday.saturday:
        return language.saturday;
      case Weekday.sunday:
        return language.sunday;
      default:
        return '';
    }
  }
}

extension WeekdayExtension on DateTime {
  Weekday get weekdayEnum {
    switch (weekday) {
      case DateTime.monday:
        return Weekday.monday;
      case DateTime.tuesday:
        return Weekday.tuesday;
      case DateTime.wednesday:
        return Weekday.wednesday;
      case DateTime.thursday:
        return Weekday.thursday;
      case DateTime.friday:
        return Weekday.friday;
      case DateTime.saturday:
        return Weekday.saturday;
      case DateTime.sunday:
        return Weekday.sunday;
      default:
        throw DateTime.monday;
    }
  }
}
