//ignore_for_file: public_member_api_docs
import 'dart:convert';

import 'package:avema_v2/application/utils/date_time_extension.dart';
import 'package:avema_v2/modal/curl_representation.dart/curl_representation.dart';
import 'package:dio/dio.dart';

class CurlLoggerDioInterceptor extends Interceptor {
  final Function(String)? printCURL;
  final Function(int, String, String, Map<String, dynamic>, List<String>)?
      onDone;
  final Function(CurlRepresentation)? onRequestAPI;

  CurlLoggerDioInterceptor({
    required this.onRequestAPI,
    required this.onDone,
    required this.printCURL,
  });

  static const apiId = 'api_Id';

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    super.onRequest(options, handler);
    // set id for API
    final id = options.extra[apiId] =
        '${options.uri.toString()}_${DateTime.now().toString()}';

    _renderCurlRepresentation(options);

    onRequestAPI?.call(
      CurlRepresentation(
        endPoint: options.uri.path,
        id: id,
        statusCode: null,
        typeOfHTTPRequest: options.method.getTypeOfHTTPRequest(),
        curl: _cURLRepresentation(options),
        response: null,
        error: '',
        dateTime: DateTime.now().taiwanFormat(),
      ),
    );
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    super.onError(err, handler);
    final id = err.response?.requestOptions.extra[apiId].toString();

    onDone?.call(
      err.response?.statusCode ?? 0,
      err.response?.statusMessage ?? 'No Status',
      id ?? 'Error Id',
      err.response?.data,
      err.response?.headers['app-version'] ?? [],
    );
  }

  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    super.onResponse(response, handler);
    final id = response.requestOptions.extra[apiId];

    onDone?.call(
      response.statusCode ?? 0,
      response.statusMessage ?? 'No Status',
      id ?? 'Error Id',
      response.data,
      response.headers['app-version'] ?? [],
    );
  }

  void _renderCurlRepresentation(RequestOptions requestOptions) {
    printCURL?.call(_cURLRepresentation(
      requestOptions,
    ));
  }

  String _cURLRepresentation(RequestOptions options) {
    final List<String> components = ['curl -i'];
    if (options.method.toUpperCase() != 'GET') {
      components.add('-X ${options.method}');
    }

    options.headers.forEach((k, v) {
      if (k != 'Cookie') {
        components.add('-H "$k: $v"');
      }
    });

    if (options.data != null) {
      // FormData can't be JSON-serialized, so keep only their fields attributes
      if (options.data is FormData) {
        options.data = Map.fromEntries((options.data as FormData).fields);
      }

      final data = json.encode(options.data).replaceAll('"', '\\"');
      components.add('-d "$data"');
    }

    components.add('"${options.uri.toString()}"');

    return components.join(' \\\n\t');
  }
}
