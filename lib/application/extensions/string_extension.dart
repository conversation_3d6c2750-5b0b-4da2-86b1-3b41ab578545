extension StringExtension on String? {
  bool search(String searchKey) {
    return this?.toLowerCase().contains(searchKey.toLowerCase()) ?? false;
  }

  int? get hour {
    if (this == null) {
      return null;
    }
    try {
      return int.parse(this!.split(':')[0]);
    } catch (e) {
      return null;
    }
  }

  int? get minute {
    if (this == null) {
      return null;
    }
    try {
      return int.parse(this!.split(':')[1].split(' ')[0]);
    } catch (e) {
      return null;
    }
  }

  String capitalizeEachWord() {
    if (this == null) {
      return '';
    }
    return this!.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }

  String convertTo24Hour() {
    if (this == null || this!.isEmpty) {
      return '';
    }

    final time12h = this!.trim();

    final parts = time12h.split(' ');
    if (parts.length != 2) {
      return time12h;
    }

    String timePart;
    String period;

    if (parts[0].contains(':')) {
      timePart = parts[0];
      period = parts[1].trim();
    } else {
      period = parts[0].trim();
      timePart = parts[1];
    }

    final timeParts = timePart.split(':');
    if (timeParts.length != 2) {
      return time12h;
    }

    int hour = int.tryParse(timeParts[0]) ?? 0;
    final minute = timeParts[1];

    if (period.toUpperCase() == 'AM' || period == "上午") {
      if (hour == 12) hour = 0;
    } else if (period.toUpperCase() == 'PM' || period == "下午") {
      if (hour != 12) hour += 12;
    }

    final result = '${hour.toString().padLeft(2, '0')}:$minute';

    return result;
  }
}
