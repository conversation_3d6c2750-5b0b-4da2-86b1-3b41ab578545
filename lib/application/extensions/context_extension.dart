import 'package:flutter/material.dart';

extension ContextExtension on BuildContext {
  Widget get verticalSpaceS => verticalSpace(valueS);
  Widget get verticalSpaceM => verticalSpace(valueM);
  Widget get verticalSpaceML => verticalSpace(valueML);
  Widget get verticalSpaceL => verticalSpace(valueL);
  Widget get verticalSpaceXL => verticalSpace(valueXL);
  Widget get verticalSpaceBottom => verticalSpace(kBottomNavigationBarHeight + valueL);

  Widget get horizontalSpaceS => horizontalSpace(valueS);
  Widget get horizontalSpaceM => horizontalSpace(valueM);
  Widget get horizontalSpaceML => horizontalSpace(valueML);
  Widget get horizontalSpaceL => horizontalSpace(valueL);
  Widget get horizontalSpaceXL => horizontalSpace(valueXL);

  Widget verticalSpace(double height) => SizedBox(height: height);
  Widget horizontalSpace(double width) => SizedBox(width: width);



  double get valueS => 4.0;
  double get valueM => 8.0;
  double get valueML => 12.0;
  double get valueL => 16.0;
  double get valueXL => 24.0;
  double get valueXXL => 32.0;
  double get height => MediaQuery.of(this).size.height;
  double get width => MediaQuery.of(this).size.width;

  EdgeInsets get paddingAllS => EdgeInsets.all(valueS);
  EdgeInsets get paddingAllM => EdgeInsets.all(valueM);
  EdgeInsets get paddingAllML => EdgeInsets.all(valueML);
  EdgeInsets get paddingAllL => EdgeInsets.all(valueL);
  EdgeInsets get paddingAllXL => EdgeInsets.all(valueXL);
  EdgeInsets get paddingAllXXL => EdgeInsets.all(valueXXL);

  EdgeInsets get pdSymVerticalS => EdgeInsets.symmetric(vertical: valueS);
  EdgeInsets get pdSymVerticalM => EdgeInsets.symmetric(vertical: valueM);
  EdgeInsets get pdSymVerticalML => EdgeInsets.symmetric(vertical: valueML);
  EdgeInsets get pdSymVerticalL => EdgeInsets.symmetric(vertical: valueL);
  EdgeInsets get pdSymVerticalXL => EdgeInsets.symmetric(vertical: valueXL);
  EdgeInsets get pdSymVerticalXXL => EdgeInsets.symmetric(vertical: valueXXL);

  EdgeInsets get pdSymHorizontalS => EdgeInsets.symmetric(horizontal: valueS);
  EdgeInsets get pdSymHorizontalM => EdgeInsets.symmetric(horizontal: valueM);
  EdgeInsets get pdSymHorizontalML => EdgeInsets.symmetric(horizontal: valueML);
  EdgeInsets get pdSymHorizontalL => EdgeInsets.symmetric(horizontal: valueL);
  EdgeInsets get pdSymHorizontalXL => EdgeInsets.symmetric(horizontal: valueXL);
  EdgeInsets get pdSymHorizontalXXL =>
      EdgeInsets.symmetric(horizontal: valueXXL);

  EdgeInsets get pdSymHorLVerML =>
      EdgeInsets.symmetric(horizontal: valueL, vertical: valueML);

  Future<dynamic> push(Widget page) async {
    final result = await Navigator.push(
      this,
      MaterialPageRoute(
        builder: (context) => page,
      ),
    );
    return result;
  }

  void unfocus() {
    FocusScope.of(this).unfocus();
  }
}
