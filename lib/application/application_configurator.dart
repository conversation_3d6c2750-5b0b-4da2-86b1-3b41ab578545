import 'package:avema_v2/application/main_application.dart';
import 'package:avema_v2/application/utils/avema_helper.dart';
import 'package:avema_v2/application/utils/local_cache.dart';
import 'package:avema_v2/features/curl_helper/application/curl_helper_bloc.dart';
import 'package:avema_v2/features/fuel_pump/application/fuel_pump_bloc.dart';
import 'package:avema_v2/features/language/custom_asset_loader.dart';
import 'package:avema_v2/features/language/data/repo/language_repo.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/locator/application/quick_send_locator_bloc.dart';
import 'package:avema_v2/features/login/application/auth_bloc.dart';
import 'package:avema_v2/features/mdvr/live_stream/applications/window_live_player_bloc.dart';
import 'package:avema_v2/features/monitor/application/bottom_sheet/bottom_sheet_bloc.dart';
import 'package:avema_v2/features/monitor/application/monitor/monitor_bloc.dart';
import 'package:avema_v2/features/monitor/application/moving_map/moving_map_bloc.dart';
import 'package:avema_v2/features/monitor/application/quick_history_waypoint/quick_history_waypoint_bloc.dart';
import 'package:avema_v2/features/monitor/application/visible_plate_monitor/visible_plate_monitor_bloc.dart';
import 'package:avema_v2/features/notification_in_app/applications/notification_from_server_bloc.dart';
import 'package:avema_v2/features/notification_in_app/applications/receiver_notification_bloc.dart';
import 'package:avema_v2/features/setting/setting_bloc.dart';
import 'package:avema_v2/features/theme/theme_bloc.dart';
import 'package:avema_v2/features/vehicle_image/applications/vehicle_image_bloc.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';

Future<void> startApplication() async {
  final locale = await _loadLanguageCache();
  await getLanguageFromServer();
  final aveLanguage = AVELanguages.instance;

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]).then((_) {
    runApp(
      EasyLocalization(
        useFallbackTranslationsForEmptyResources: true,
        useFallbackTranslations: true,
        startLocale: locale,
        fallbackLocale: aveLanguage.chineseLocale,
        supportedLocales: [
          aveLanguage.engLocale,
          aveLanguage.chineseLocale,
          aveLanguage.vnLocale,
        ],
        path: aveLanguage.translationPath,
        assetLoader: CustomAssetLoader(),
        child: MultiBlocProvider(
          providers: [
            /// SETTING \\\
            BlocProvider<ThemeBloc>(
              create: (context) => getIt<ThemeBloc>(),
            ),
            BlocProvider<SettingBloc>(
              create: (context) => getIt<SettingBloc>(),
            ),
            BlocProvider<CurlHelperBloc>(
              create: (context) => getIt<CurlHelperBloc>(),
            ),

            /// NOTIFICATION \\\
            BlocProvider<NotificationFromServerBloc>(
              create: (context) => getIt<NotificationFromServerBloc>(),
            ),
            BlocProvider<ReceiverNotificationBloc>(
              create: (context) => getIt<ReceiverNotificationBloc>(),
            ),

            /// AUTH \\\
            BlocProvider<AuthBloc>(
              create: (context) => getIt<AuthBloc>(),
            ),

            /// MONITOR \\\
            BlocProvider<MonitorBloc>(
              create: (context) => getIt<MonitorBloc>(),
            ),
            BlocProvider<BottomSheetBloc>(
              create: (context) => getIt<BottomSheetBloc>(),
            ),

            /// GOOGLE MAP \\\
            BlocProvider<VisiblePlateMonitorBloc>(
              create: (context) => getIt<VisiblePlateMonitorBloc>(),
            ),
            BlocProvider<MovingMapBloc>(
              create: (context) => getIt<MovingMapBloc>(),
            ),

            /// MDVR \\\
            BlocProvider<WindowLivePlayerBloc>(
              create: (context) => getIt<WindowLivePlayerBloc>(),
            ),

            /// IMAGE \\\
            BlocProvider<VehicleImageBloc>(
              create: (context) => getIt<VehicleImageBloc>(),
            ),

            /// LOCATOR \\\
            BlocProvider<QuickSendLocatorBloc>(
              create: (context) => getIt<QuickSendLocatorBloc>(),
            ),

            /// QUICK HISTORY \\\
            BlocProvider<QuickHistoryWaypointBloc>(
              create: (context) => getIt<QuickHistoryWaypointBloc>(),
            ),

            /// FUEL PUMP \\\
            BlocProvider<FuelPumpBloc>(
              create: (context) => getIt<FuelPumpBloc>(),
            ),
          ],
          child: const MainApplication(),
        ),
      ),
    );
  });
}

Future<Locale> _loadLanguageCache() async {
  final localCache = getIt<LocalCache>();
  final aveLanguage = AVELanguages.instance;

  final language = await localCache.getLanguage();

  if (language != null) {
    if (language == Language.chinese.toStringValue()) {
      return aveLanguage.chineseLocale;
    }
    if (language == Language.english.toStringValue()) {
      return aveLanguage.engLocale;
    }
    if (language == Language.vietnamese.toStringValue()) {
      return aveLanguage.vnLocale;
    }
  }
  return aveLanguage.chineseLocale;
}

Future<void> getLanguageFromServer() async {
  // 1. GET LANGUAGE FROM SERVER
  final responseLanguage =
      await getIt<ILanguageRepository>().getLanguageFromServer(
    languageSupports: [
      AVELanguages.chineseKey,
      AVELanguages.englishKey,
      AVELanguages.vietnamKey,
    ],
  );

  dynamic enDataFromServer;
  dynamic viDataFromServer;
  dynamic zhDataFromServer;

  try {
    final directory = await getApplicationDocumentsDirectory();
    final basePath = directory.path;

    responseLanguage.fold(
      (failure) {
        AvemaHelper.logError(
          error: '$failure',
          title: 'LANGUAGE',
          errorCode: 'LANG-000',
        );

        enDataFromServer = {};
        viDataFromServer = {};
        zhDataFromServer = {};
      },
      (result) async {
        enDataFromServer = result[AVELanguages.englishKey];
        viDataFromServer = result[AVELanguages.vietnamKey];
        zhDataFromServer = result[AVELanguages.chineseKey];
      },
    );

    await AVELanguages.syncJsonDataBothSide(
      basePath: '$basePath${AVELanguages.enJsonPath}',
      dataFromServer: enDataFromServer,
      localLanguagePath:
          '${AVELanguages.languagePath}${AVELanguages.enJsonPath}',
    );
    await AVELanguages.syncJsonDataBothSide(
      basePath: '$basePath${AVELanguages.viJsonPath}',
      dataFromServer: viDataFromServer,
      localLanguagePath:
          '${AVELanguages.languagePath}${AVELanguages.viJsonPath}',
    );

    await AVELanguages.syncJsonDataBothSide(
      basePath: '$basePath${AVELanguages.zhJsonPath}',
      dataFromServer: zhDataFromServer,
      localLanguagePath:
          '${AVELanguages.languagePath}${AVELanguages.zhJsonPath}',
    );
  } catch (e) {
    enDataFromServer = {};
    viDataFromServer = {};
    zhDataFromServer = {};

    final directory = await getApplicationDocumentsDirectory();
    final basePath = directory.path;

    await AVELanguages.syncJsonDataBothSide(
      basePath: '$basePath${AVELanguages.enJsonPath}',
      dataFromServer: enDataFromServer,
      localLanguagePath:
          '${AVELanguages.languagePath}${AVELanguages.enJsonPath}',
    );
    await AVELanguages.syncJsonDataBothSide(
      basePath: '$basePath${AVELanguages.viJsonPath}',
      dataFromServer: viDataFromServer,
      localLanguagePath:
          '${AVELanguages.languagePath}${AVELanguages.viJsonPath}',
    );

    await AVELanguages.syncJsonDataBothSide(
      basePath: '$basePath${AVELanguages.zhJsonPath}',
      dataFromServer: zhDataFromServer,
      localLanguagePath:
          '${AVELanguages.languagePath}${AVELanguages.zhJsonPath}',
    );

    AvemaHelper.logError(
      error: e.toString(),
      title: 'LANGUAGE',
      errorCode: 'LANG-001',
    );
  }
}
