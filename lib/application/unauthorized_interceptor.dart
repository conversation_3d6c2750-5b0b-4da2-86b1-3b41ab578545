import 'dart:async';

import 'package:dio/dio.dart';

import 'package:avema_v2/application/injection_module.dart';
import 'package:avema_v2/application/utils/local_cache.dart';
import 'package:avema_v2/features/firebase/avema_firebase.dart';
import 'package:avema_v2/features/login/application/auth_bloc.dart';
import 'package:avema_v2/injection/injection.dart';

import 'utils/avema_helper.dart';

class UnauthorizedInterceptor extends Interceptor {
  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    try {
      final response = err.response;
      final statusCode = response?.statusCode;
      final receivedUnauthorized = statusCode == AvemaAPI.unAuthorized;
      if (!receivedUnauthorized) {
        return super.onError(err, handler);
      }

      final avemaUser = await getIt<LocalCache>().getAvemaUser();
      final password = avemaUser?.password;
      final username = avemaUser?.username;
      final firebaseToken = AvemaFirebase.instance.firebaseToken;
      final authBloc = getIt<AuthBloc>();
      final authBlocState = authBloc.state;

      final isUnauthorized = authBlocState.maybeMap(
        orElse: () => false,
        authenticated: (_) => false,
        unauthenticated: (_) => true,
      );

      final missingCredentials = avemaUser == null ||
          password == null ||
          username == null ||
          firebaseToken == null;

      if (missingCredentials || isUnauthorized) {
        return super.onError(err, handler);
      }

      final completer = Completer<bool>();

      authBloc.add(
        AuthEvent.login(
          username: username,
          password: password,
          firebaseToken: firebaseToken,
          savePassword: true,
        ),
      );

      final subscription = authBloc.stream.listen((state) {
        state.maybeMap(
          authenticated: (_) => completer.complete(true),
          unauthenticated: (_) => completer.complete(false),
          orElse: () {},
        );
      });

      final isAuthenticated = await completer.future;

      await subscription.cancel();

      if (!isAuthenticated) {
        AvemaHelper.onUnAuthenticated?.call();
        return super.onError(err, handler);
      }

      final updatedUser = await getIt<LocalCache>().getAvemaUser();
      if (updatedUser != null) {
        AvemaHelper.setDioAuthHeaders(
          accessToken: updatedUser.accessToken,
          username: updatedUser.username,
        );
        final response = await _retry(err.requestOptions);
        return handler.resolve(response);
      }

      return super.onError(err, handler);
    } catch (e) {
      return super.onError(err, handler);
    }
  }

  Future<Response<dynamic>> _retry(RequestOptions requestOptions) async {
    final options = Options(
      method: requestOptions.method,
      extra: requestOptions.extra,
    );
    return getIt<Dio>().request<dynamic>(
      AvemaAPI.baseUrl + requestOptions.path,
      data: requestOptions.data,
      queryParameters: requestOptions.queryParameters,
      options: options,
    );
  }
}
