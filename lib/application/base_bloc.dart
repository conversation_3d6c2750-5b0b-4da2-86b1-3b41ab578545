import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

abstract class BaseBloc<Event, State> extends Bloc<Event, State> {
  BaseBloc(super.initialState);

  Future<void> handleRepositoryCall<T, F>({
    required Future<Either<F, T>> Function() call,
    required void Function(T data) onSuccess,
    required Emitter<State> emit,
    required State Function(
      State currentState, {
      bool? isLoading,
      F? failure
    }) updateState,
    bool? loading,
    void Function(F failure)? onFailure,
  }) async {
    emit(updateState(state, isLoading: loading ?? true));

    final response = await call();

    response.fold(
      (failure) {
        onFailure?.call(failure);
        emit(updateState(state, isLoading: false, failure: failure));
      },
      (data) {
        onSuccess(data);
        emit(updateState(state, isLoading: false));
      },
    );
  }
}
