import 'package:avema_v2/features/login/application/auth_bloc.dart';
import 'package:avema_v2/features/notification_in_app/applications/notification_from_server_bloc.dart';
import 'package:avema_v2/features/notification_in_app/applications/receiver_notification_bloc.dart';
import 'package:avema_v2/features/notification_in_app/presentations/notification_widget.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:overlay_support/overlay_support.dart';

class GlobalBlocListener extends StatelessWidget {
  final Widget child;
  const GlobalBlocListener({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<AuthBloc, AuthState>(
          bloc: getIt<AuthBloc>(),
          listener: (context, authState) {},
          listenWhen: (previous, current) {
            //FIREBASE
            if (authenticatedTrigger(previous, current)) {
              initializeLocalNotification();
            }
            return previous != current;
          },
        ),
        BlocListener<ReceiverNotificationBloc, ReceiverNotificationState>(
          bloc: getIt<ReceiverNotificationBloc>(),
          listener: (context, notificationInAppState) {
            final notiMessage = notificationInAppState.listFirebaseMessage.last;
            showNotificationInApp(notiMessage);

            // if (Platform.isAndroid) {
            showOverlayNotification(
              (context) {
                return NotificationWidget(
                  firebaseMessage: notiMessage,
                );
              },
              duration: const Duration(seconds: 3),
            );
            // }

            getIt<NotificationFromServerBloc>()
                .add(const NotificationFromServerEvent.getAllNotification());
          },
          listenWhen: (previous, current) {
            return previous.listFirebaseMessage.length ==
                current.listFirebaseMessage.length - 1;
          },
        ),
      ],
      child: child,
    );
  }

  //FIREBASE

  Future<void> showNotificationInApp(RemoteMessage notiMessage) async {
    getIt<ReceiverNotificationBloc>()
        .add(ReceiverNotificationEvent.showNotificationInApp(
      firebaseMessage: notiMessage,
    ));
  }

  Future<void> initializeLocalNotification() async {
    getIt<ReceiverNotificationBloc>()
        .add(const ReceiverNotificationEvent.initializeLocalNotification());
  }

  bool authenticatedTrigger(AuthState previous, AuthState current) {
    final bool isNotAuthenticated = previous.maybeMap(
        orElse: () => true,
        authenticated: (auth) {
          return false;
        });

    final bool isCurrentAuthenticated = current.maybeMap(
        orElse: () => false,
        authenticated: (auth) {
          return true;
        });

    if (isCurrentAuthenticated && isNotAuthenticated) {
      return true;
    }
    return false;
  }

}
