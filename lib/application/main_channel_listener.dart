import 'dart:async';

import 'package:avema_v2/features/app_permission/manage_app_permission.dart';
import 'package:avema_v2/features/avema_map/avema_map_helper.dart';
import 'package:avema_v2/features/firebase/avema_firebase.dart';
import 'package:avema_v2/features/language/languages_uti.dart';
import 'package:avema_v2/features/login/application/auth_bloc.dart';
import 'package:avema_v2/features/monitor/application/manage_location/manage_location.dart';
import 'package:avema_v2/features/notification_in_app/applications/notification_from_server_bloc.dart';
import 'package:avema_v2/features/notification_in_app/applications/receiver_notification_bloc.dart';
import 'package:avema_v2/injection/injection.dart';
import 'package:rxdart/rxdart.dart';

class GlobalListenerManager {
  final CompositeSubscription _disposeBag = CompositeSubscription();

  void registerGlobalListener() {
    // FIREBASE
    AvemaFirebase.instance.listenFirebaseMessaging(
      (message) {
        getIt<ReceiverNotificationBloc>()
            .add(ReceiverNotificationEvent.receivedNotification(
          firebaseMessage: message,
        ));
      },
    );

    // CHANGE LANGUAGE
    AVELanguages.instance.onLocaleChanged = () {
      getIt<AuthBloc>().state.maybeMap(
            orElse: () {},
            authenticated: (_) {
              getIt<NotificationFromServerBloc>().add(
                const NotificationFromServerEvent.initial(),
              );
            },
          );
    };
  }
  // LOCATION \\

  Timer? timerGetLocation;
  bool _isLocationListenerActive = false;

  Future<void> locationListener() async {
    final managePermission = ManageAppPermission.instance;
    final manageLocation = ManageLocation.instance;
    await managePermission.checkLocationService();
    if (!managePermission.isGrandedPermission) {
      return;
    }

    _isLocationListenerActive = true;
    bool isProcessing = false;
    timerGetLocation = Timer.periodic(
      const Duration(seconds: AvemaMapHelper.timerValue),
      (_) async {
        if (!_isLocationListenerActive || isProcessing) return;

        isProcessing = true;

        try {
          if (!_isLocationListenerActive) return;

          if (!_isLocationListenerActive ||
              manageLocation.streamIsGettingData.isClosed) {
            return;
          }
          manageLocation.streamIsGettingData.add(true);

          await manageLocation.getLocationData().whenComplete(() async {
            if (!_isLocationListenerActive) return;

            await Future.delayed(const Duration(milliseconds: 600));

            if (!_isLocationListenerActive ||
                manageLocation.streamIsGettingData.isClosed) {
              return;
            }
            manageLocation.streamIsGettingData.add(false);
          });
        } catch (e) {
          manageLocation.streamIsGettingData.add(false);
        } finally {
          isProcessing = false;
        }
      },
    );
  }

  void disposeLocationListener() {
    _isLocationListenerActive = false;

    if (timerGetLocation != null) {
      timerGetLocation!.cancel();
      timerGetLocation = null;
    }

    ManageLocation.instance.close();
  }

  // ----- * ----- \\

  void disposeAllSub() async {
    disposeLocationListener();
    await _disposeBag.clear();
    await _disposeBag.dispose();
  }
}
