import 'package:flutter/material.dart';

mixin ScrollMixin<T extends StatefulWidget> on State<T> {
  late ScrollController _scrollController;

  ScrollController get scrollController => _scrollController;

  void initScroll(void Function()? onScroll) {
    _scrollController = ScrollController();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        onScroll?.call();
      }
    });
  }

  void disposeScroll() {
    _scrollController.dispose();
  }
}
