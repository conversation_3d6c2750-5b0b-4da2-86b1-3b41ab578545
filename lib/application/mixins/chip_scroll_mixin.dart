import 'package:flutter/material.dart';

mixin ChipScrollMixin<T extends StatefulWidget> on State<T> {
  late ScrollController _chipScrollController;
  final Map<int, GlobalKey> _chipKeys = {};

  ScrollController get chipController => _chipScrollController;

  Map<int, GlobalKey> get chipKeys => _chipKeys;

  void initChipScroll() {
    _chipScrollController = ScrollController();
  }

  void disposeChipScroll() {
    _chipScrollController.dispose();
  }

  GlobalKey getChipKey(int index) {
    if (!_chipKeys.containsKey(index)) {
      _chipKeys[index] = GlobalKey();
    }
    return _chipKeys[index]!;
  }

  double getChipWidth(int index) {
    final key = _chipKeys[index];
    if (key?.currentContext != null) {
      final RenderBox renderBox =
          key!.currentContext!.findRenderObject() as RenderBox;
      return renderBox.size.width;
    }
    return 120.0;
  }

  void scrollToSelectedChip(
    int selectedIndex, {
    double spacing = 8.0,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    if (selectedIndex == -1 || !_chipScrollController.hasClients) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_chipScrollController.hasClients) return;

      double offset = 0;
      for (int i = 0; i < selectedIndex; i++) {
        offset += getChipWidth(i) + spacing;
      }

      final currentChipWidth = getChipWidth(selectedIndex);
      final viewportWidth = _chipScrollController.position.viewportDimension;

      final centerOffset =
          offset - (viewportWidth / 2) + (currentChipWidth / 2);

      final maxScroll = _chipScrollController.position.maxScrollExtent;
      final minScroll = _chipScrollController.position.minScrollExtent;
      final targetOffset = centerOffset.clamp(minScroll, maxScroll);

      _chipScrollController.animateTo(
        targetOffset,
        duration: duration,
        curve: curve,
      );
    });
  }

  void clearChipKeys() {
    _chipKeys.clear();
  }
}
