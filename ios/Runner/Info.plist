<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>我們需要您提供定位權限，用於系統使用期間在地圖上顯示您的位置當前位置及車輛的相對位.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>請提供位置權限，讓我們可以於地圖中顯示您的位置及您與設備之相對位置.</string>
	<key>NSUserTrackingUsageDescription</key>
<string>噗噗龍需要追蹤您的裝置，作為日後分析與除錯使用</string>
<key>NSPhotoLibraryUsageDescription</key>
	<string>噗噗龍 需要使用 圖片與圖庫來存取從車機下載的圖片</string>
<key>NSLocationAlwaysUsageDescription</key>
<string>在系統於背景執行時，我們需要您的定位功能來顯示您的當前位置於地圖</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
    	<string>remote-notification</string>
	</array>
	<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleAllowMixedLocalizations</key>
<true/>
	<key>CFBundleDisplayName</key>
	<string>噗噗龍</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>nb</string>
	</array>
	<key>CFBundleName</key>
	<string>flutter_tracking_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<key>NSUserNotificationCenterUsageDescription</key>
<string>Allow notifications to be displayed even when the app is in the background.</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
</dict>
</plist>
